/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as chat from "../chat.js";
import type * as cleaning from "../cleaning.js";
import type * as http from "../http.js";
import type * as inspections from "../inspections.js";
import type * as organizations from "../organizations.js";
import type * as properties from "../properties.js";
import type * as test_multi_tenant from "../test_multi_tenant.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  chat: typeof chat;
  cleaning: typeof cleaning;
  http: typeof http;
  inspections: typeof inspections;
  organizations: typeof organizations;
  properties: typeof properties;
  test_multi_tenant: typeof test_multi_tenant;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
