"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"

interface ErrorBoundaryProps {
  error: Error & { digest?: string }
  reset: () => void
}

export function ErrorBoundary({ error, reset }: ErrorBoundaryProps) {
  const [errorDetails, setErrorDetails] = useState<string>("")
  const [isSSRError, setIsSSRError] = useState<boolean>(false)

  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Application error:", error)

    // Check if this is a server component error
    const isServerError = error.message?.includes('Error during SSR') || 
                          error.message?.includes('server component') ||
                          error.digest?.includes('server');

    setIsSSRError(isServerError || false);

    // Format error details for display
    let displayMessage = error.message || "An unknown error occurred";
    
    // Remove sensitive or technical details for production
    if (process.env.NODE_ENV === 'production') {
      // For server errors, provide a more user-friendly message
      if (isServerError) {
        displayMessage = "The application encountered a server-side rendering issue. This is likely due to a configuration problem."
      } else if (displayMessage.includes('navigator') || displayMessage.includes('mediaDevices')) {
        displayMessage = "The application requires browser capabilities that weren't available during rendering."
      }
    }

    setErrorDetails(displayMessage)
  }, [error])

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-6 bg-gradient-to-b from-black to-zinc-900">
      <div className="w-20 h-20 rounded-2xl bg-zinc-900 flex items-center justify-center mb-6 shadow-lg">
        <svg viewBox="0 0 24 24" className="w-12 h-12">
          <path d="M12 8L12 12" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
          <path d="M12 16L12 16.01" stroke="#A4D321" strokeWidth="2" strokeLinecap="round" />
          <circle cx="12" cy="12" r="9" stroke="#3D4D61" strokeWidth="2" fill="none" />
        </svg>
      </div>

      <h1 className="text-white text-2xl font-bold mb-4 text-center">Something went wrong</h1>
      <p className="text-zinc-400 text-center mb-8 max-w-md">{errorDetails}</p>

      {!isSSRError && (
        <motion.button
          className="py-3 px-8 bg-gradient-to-r from-[#3D4D61] to-[#A4D321] text-white font-medium rounded-md"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={reset}
        >
          Try Again
        </motion.button>
      )}

      {isSSRError && (
        <motion.button
          className="py-3 px-8 bg-gradient-to-r from-[#3D4D61] to-[#A4D321] text-white font-medium rounded-md"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => window.location.href = '/'}
        >
          Return to Home
        </motion.button>
      )}

      <div className="mt-8 px-4 py-2 bg-zinc-800 rounded-md">
        <details className="text-sm text-zinc-400">
          <summary className="cursor-pointer">Technical Details</summary>
          <pre className="mt-2 p-2 bg-black rounded overflow-x-auto max-w-lg">
            {error.stack || error.message || "No stack trace available"}
            {error.digest && <div className="mt-2">Digest: {error.digest}</div>}
          </pre>
        </details>
      </div>
    </div>
  )
}