import { describe, it, expect, vi } from 'vitest';
import { userRepository } from '@/app/repositories/user-repository';
import { propertyRepository } from '@/app/repositories/property-repository';
import { authService } from '@/app/services/auth-service';
import { api } from '@/convex/_generated/api';
import { createMockUser, createMockProperty, measurePerformance } from '../test-utils';

// Mock the Convex API
vi.mock('@/convex/_generated/api', () => ({
  api: {
    users: {
      findByEmail: vi.fn(),
      findById: vi.fn(),
      findByClerkId: vi.fn(),
      getCurrentUser: vi.fn(),
      listUsers: vi.fn(),
      searchUsers: vi.fn(),
      updateProfile: vi.fn(),
      findByRole: vi.fn(),
      findByDepartment: vi.fn(),
    },
    properties: {
      getProperty: vi.fn(),
      listProperties: vi.fn(),
      createProperty: vi.fn(),
      updateProperty: vi.fn(),
      deleteProperty: vi.fn(),
      searchProperties: vi.fn(),
      getPropertiesByManager: vi.fn(),
      getPropertiesByRegion: vi.fn(),
      getPropertiesByType: vi.fn(),
      getPropertiesByStatus: vi.fn(),
      createPropertyArea: vi.fn(),
      getPropertyAreas: vi.fn(),
      updatePropertyArea: vi.fn(),
      deletePropertyArea: vi.fn(),
    },
  },
}));

describe('Performance Tests', () => {
  describe('User Repository Performance', () => {
    it('should measure findByEmail performance', async () => {
      const mockUser = createMockUser({ email: '<EMAIL>' });
      (api.users.findByEmail as any).mockResolvedValue(mockUser);

      const performance = await measurePerformance(
        () => userRepository.findByEmail('<EMAIL>'),
        100
      );

      expect(performance.average).toBeLessThan(100); // Less than 100ms average
      expect(performance.min).toBeGreaterThanOrEqual(0);
      expect(performance.max).toBeLessThan(500); // Less than 500ms max
      expect(performance.results).toHaveLength(100);
    });

    it('should measure search performance', async () => {
      const mockUsers = Array.from({ length: 50 }, (_, i) => 
        createMockUser({ name: `User ${i}`, email: `user${i}@example.com` })
      );
      (api.users.searchUsers as any).mockResolvedValue(mockUsers);

      const performance = await measurePerformance(
        () => userRepository.search('test'),
        50
      );

      expect(performance.average).toBeLessThan(200); // Less than 200ms for search
      expect(performance.results).toHaveLength(50);
      expect(performance.results[0]).toHaveLength(50);
    });

    it('should measure listUsers performance', async () => {
      const mockUsers = Array.from({ length: 100 }, (_, i) => 
        createMockUser({ name: `User ${i}` })
      );
      (api.users.listUsers as any).mockResolvedValue(mockUsers);

      const performance = await measurePerformance(
        () => userRepository.getAll(),
        30
      );

      expect(performance.average).toBeLessThan(300); // Less than 300ms for 100 users
      expect(performance.results).toHaveLength(30);
      expect(performance.results[0]).toHaveLength(100);
    });
  });

  describe('Property Repository Performance', () => {
    it('should measure findById performance', async () => {
      const mockProperty = createMockProperty({ _id: 'prop123' as any });
      (api.properties.getProperty as any).mockResolvedValue(mockProperty);

      const performance = await measurePerformance(
        () => propertyRepository.findById('prop123' as any),
        100
      );

      expect(performance.average).toBeLessThan(100);
      expect(performance.results).toHaveLength(100);
    });

    it('should measure search performance', async () => {
      const mockProperties = Array.from({ length: 100 }, (_, i) => 
        createMockProperty({ name: `Property ${i}`, address: `Address ${i}` })
      );
      (api.properties.searchProperties as any).mockResolvedValue(mockProperties);

      const performance = await measurePerformance(
        () => propertyRepository.search('test'),
        50
      );

      expect(performance.average).toBeLessThan(250);
      expect(performance.results).toHaveLength(50);
      expect(performance.results[0]).toHaveLength(100);
    });

    it('should measure listProperties performance', async () => {
      const mockProperties = Array.from({ length: 500 }, (_, i) => 
        createMockProperty({ name: `Property ${i}` })
      );
      (api.properties.listProperties as any).mockResolvedValue(mockProperties);

      const performance = await measurePerformance(
        () => propertyRepository.getAll(),
        20
      );

      expect(performance.average).toBeLessThan(500); // Less than 500ms for 500 properties
      expect(performance.results).toHaveLength(20);
      expect(performance.results[0]).toHaveLength(500);
    });

    it('should measure getPropertiesByRegion performance', async () => {
      const mockProperties = Array.from({ length: 200 }, (_, i) => 
        createMockProperty({ region: 'Sydney', name: `Sydney Property ${i}` })
      );
      (api.properties.getPropertiesByRegion as any).mockResolvedValue(mockProperties);

      const performance = await measurePerformance(
        () => propertyRepository.getByRegion('Sydney'),
        30
      );

      expect(performance.average).toBeLessThan(200);
      expect(performance.results).toHaveLength(30);
      expect(performance.results[0]).toHaveLength(200);
    });
  });

  describe('Auth Service Performance', () => {
    it('should measure login performance', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        password: 'password123',
        role: 'User',
      };

      (userRepository.findByEmail as any).mockResolvedValue(mockUser);

      const performance = await measurePerformance(
        () => authService.login('<EMAIL>', 'password123'),
        50
      );

      expect(performance.average).toBeLessThan(150);
      expect(performance.results).toHaveLength(50);
      expect(performance.results[0]).toBeDefined();
    });

    it('should measure registration performance', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      };

      (userRepository.findByEmail as any).mockResolvedValue(null);
      const mockUser = { id: 'user123', ...userData };
      (userRepository.findByEmail as any).mockResolvedValueOnce(mockUser);

      const performance = await measurePerformance(
        () => authService.register(userData),
        30
      );

      expect(performance.average).toBeLessThan(200);
      expect(performance.results).toHaveLength(30);
    });

    it('should measure authentication check performance', async () => {
      const mockCookies = {
        get: vi.fn().mockReturnValue({ value: 'eyJ1c2VySWQiOiJ1c2VyMTIzIiwiZXhwIjoxOTk5OTk5OTk5fQ==' }),
      };
      (require('next/headers').cookies as any).mockReturnValue(mockCookies);

      const mockUser = { id: 'user123', role: 'User' };
      (userRepository.findById as any).mockResolvedValue(mockUser);

      const performance = await measurePerformance(
        () => authService.isAuthenticated(),
        100
      );

      expect(performance.average).toBeLessThan(100);
      expect(performance.results).toHaveLength(100);
    });
  });

  describe('Database Query Performance', () => {
    it('should measure complex query performance', async () => {
      const mockUsers = Array.from({ length: 1000 }, (_, i) => 
        createMockUser({ 
          name: `User ${i}`, 
          email: `user${i}@example.com`,
          department: i % 5 === 0 ? 'IT' : 'HR'
        })
      );
      (api.users.listUsers as any).mockResolvedValue(mockUsers);

      const performance = await measurePerformance(
        () => userRepository.findByDepartment('IT'),
        20
      );

      expect(performance.average).toBeLessThan(300);
      expect(performance.results).toHaveLength(20);
      expect(performance.results[0]).toHaveLength(200); // 1000/5 = 200 IT users
    });

    it('should measure pagination performance', async () => {
      const mockProperties = Array.from({ length: 1000 }, (_, i) => 
        createMockProperty({ name: `Property ${i}` })
      );
      (api.properties.listProperties as any).mockResolvedValue(mockProperties);

      const performance = await measurePerformance(
        () => propertyRepository.getAll({ limit: 50 }),
        25
      );

      expect(performance.average).toBeLessThan(200);
      expect(performance.results).toHaveLength(25);
      expect(performance.results[0]).toHaveLength(50);
    });

    it('should measure search with filters performance', async () => {
      const mockProperties = Array.from({ length: 500 }, (_, i) => 
        createMockProperty({ 
          name: `Property ${i}`,
          region: i % 2 === 0 ? 'Sydney' : 'Melbourne',
          type: i % 3 === 0 ? 'Office' : 'Retail'
        })
      );
      (api.properties.searchProperties as any).mockResolvedValue(mockProperties);

      const performance = await measurePerformance(
        () => propertyRepository.search('office sydney'),
        15
      );

      expect(performance.average).toBeLessThan(300);
      expect(performance.results).toHaveLength(15);
    });
  });

  describe('Memory Usage Performance', () => {
    it('should handle large datasets efficiently', async () => {
      const largeUser = createMockUser({
        preferences: { 
          theme: 'dark', 
          notifications: true, 
          data: new Array(10000).fill('test'),
          settings: { language: 'en', timezone: 'UTC' }
        }
      });
      
      (api.users.findByEmail as any).mockResolvedValue(largeUser);

      const performance = await measurePerformance(
        () => userRepository.findByEmail('<EMAIL>'),
        10
      );

      expect(performance.average).toBeLessThan(100);
      expect(performance.results).toHaveLength(10);
      expect(performance.results[0]?.preferences?.data).toHaveLength(10000);
    });

    it('should handle bulk operations efficiently', async () => {
      const mockProperties = Array.from({ length: 1000 }, (_, i) => 
        createMockProperty({ name: `Bulk Property ${i}` })
      );
      (api.properties.listProperties as any).mockResolvedValue(mockProperties);

      const performance = await measurePerformance(
        () => propertyRepository.getAll(),
        5
      );

      expect(performance.average).toBeLessThan(1000);
      expect(performance.results).toHaveLength(5);
      expect(performance.results[0]).toHaveLength(1000);
    });
  });

  describe('Concurrent Performance', () => {
    it('should maintain performance under concurrent load', async () => {
      const mockUser = createMockUser();
      (api.users.findByEmail as any).mockResolvedValue(mockUser);

      const promises = Array.from({ length: 20 }, () => 
        userRepository.findByEmail('<EMAIL>')
      );

      const start = Date.now();
      const results = await Promise.all(promises);
      const end = Date.now();

      expect(results).toHaveLength(20);
      expect(end - start).toBeLessThan(1000); // All 20 requests should complete within 1 second
      results.forEach(result => {
        expect(result).toEqual(mockUser);
      });
    });

    it('should maintain performance with mixed operations', async () => {
      const mockUser = createMockUser();
      const mockProperty = createMockProperty();
      
      (api.users.findByEmail as any).mockResolvedValue(mockUser);
      (api.properties.getProperty as any).mockResolvedValue(mockProperty);

      const operations = [
        () => userRepository.findByEmail('<EMAIL>'),
        () => propertyRepository.findById('prop123' as any),
        () => userRepository.search('test'),
        () => propertyRepository.getAll(),
      ];

      const promises = Array.from({ length: 30 }, () => 
        operations[Math.floor(Math.random() * operations.length)]()
      );

      const start = Date.now();
      const results = await Promise.all(promises);
      const end = Date.now();

      expect(results).toHaveLength(30);
      expect(end - start).toBeLessThan(2000); // All 30 mixed operations within 2 seconds
    });
  });

  describe('Performance Benchmarks', () => {
    it('should meet response time benchmarks', async () => {
      const benchmarks = {
        userFindByEmail: 50, // 50ms
        userSearch: 100, // 100ms
        propertyFindById: 50, // 50ms
        propertySearch: 150, // 150ms
        authLogin: 100, // 100ms
        authRegister: 150, // 150ms
      };

      const mockUser = createMockUser();
      const mockProperty = createMockProperty();
      
      (api.users.findByEmail as any).mockResolvedValue(mockUser);
      (api.properties.getProperty as any).mockResolvedValue(mockProperty);
      (userRepository.findByEmail as any).mockResolvedValue(mockUser);

      // Test user findByEmail
      const userFindPerf = await measurePerformance(
        () => userRepository.findByEmail('<EMAIL>'),
        10
      );
      expect(userFindPerf.average).toBeLessThan(benchmarks.userFindByEmail);

      // Test property findById
      const propertyFindPerf = await measurePerformance(
        () => propertyRepository.findById('prop123' as any),
        10
      );
      expect(propertyFindPerf.average).toBeLessThan(benchmarks.propertyFindById);

      // Test auth login
      const authLoginPerf = await measurePerformance(
        () => authService.login('<EMAIL>', 'password123'),
        10
      );
      expect(authLoginPerf.average).toBeLessThan(benchmarks.authLogin);
    });

    it('should maintain performance with large datasets', async () => {
      const largeDataset = Array.from({ length: 10000 }, (_, i) => 
        createMockUser({ email: `user${i}@example.com` })
      );
      (api.users.listUsers as any).mockResolvedValue(largeDataset);

      const performance = await measurePerformance(
        () => userRepository.getAll(),
        3
      );

      expect(performance.average).toBeLessThan(2000); // Less than 2 seconds for 10k users
      expect(performance.results).toHaveLength(3);
      expect(performance.results[0]).toHaveLength(10000);
    });
  });
});