import { MobileAIInterface } from "@/components/mobile-ai-interface"
import { auth } from "@clerk/nextjs/server"
import { db, messages } from "@/app/db"
import { eq } from "drizzle-orm"
import { createUserMessage, deleteUserMessage } from "./actions"
import { headers } from 'next/headers';
import { redirect } from "next/navigation"
export default async function Home() {
  // Explicitly await headers before potentially using them implicitly via auth()
  await headers();

  const authResult = auth();
  const { userId, orgId } = authResult;

  // If the user is authenticated but has no active organization,
  // redirect to the organization selection page
  if (userId && !orgId) {
    return (
      <div className="min-h-screen w-full bg-background flex flex-col items-center justify-center p-4">
        <div className="bg-zinc-900 rounded-xl p-6 border border-zinc-800 max-w-lg w-full">
          <h1 className="text-xl font-semibold mb-4 text-center">Welcome to ARA Property Services</h1>
          <p className="text-zinc-400 mb-6 text-center">
            Please select or create an organization to continue.
          </p>
          <div className="flex justify-center">
            <a 
              href="/organization/settings" 
              className="inline-flex items-center px-4 py-2 bg-[#A4D321] hover:bg-[#BFE550] text-black font-medium rounded-full"
            >
              Manage Organizations
            </a>
          </div>
        </div>
      </div>
    );
  }

  let existingMessage: { content_transcript: string } | null = null;
  if (userId) {
    const messageResult = await db.query.messages.findFirst({
      where: eq(messages.sender_id, userId),
      columns: {
        content_transcript: true,
      },
    });
    
    if (messageResult && messageResult.content_transcript) {
      existingMessage = { content_transcript: messageResult.content_transcript };
    }
  }

  // Organization info fetching removed due to schema mismatch
  const organizationInfo = null;

  return (
    <div className="min-h-screen w-full bg-background flex flex-col">
      {/* Organization banner removed due to schema mismatch */}

      {/* Demo message UI */}
      <div className="w-full border-b border-zinc-800 p-4">
        {existingMessage ? (
          <div className="space-y-2">
            <p className="text-sm text-zinc-400">Your saved message:</p>
            <p className="font-medium text-white">{existingMessage.content_transcript}</p>
            <form action={deleteUserMessage}>
              <button className="mt-2 rounded bg-red-600 px-3 py-1 text-sm text-white hover:bg-red-700">
                Delete Message
              </button>
            </form>
          </div>
        ) : (
          <form action={createUserMessage} className="flex gap-2">
            <input
              type="text"
              name="message"
              placeholder="Enter a message"
              className="flex-1 rounded border border-zinc-700 bg-zinc-900 p-2 text-sm text-white focus:outline-none"
              required
            />
            <button className="rounded bg-green-600 px-3 py-1 text-sm text-white hover:bg-green-700">
              Save
            </button>
          </form>
        )}
      </div>

      {/* Existing mobile AI interface */}
      <div className="flex-1">
        <MobileAIInterface />
      </div>
    </div>
  )
}
