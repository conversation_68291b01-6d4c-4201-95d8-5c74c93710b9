import { describe, it, expect, vi, beforeEach } from 'vitest';
import { userRepository } from '@/app/repositories/user-repository';
import { api } from '@/convex/_generated/api';
import { createMockUser } from '../test-utils';

// Mock the Convex API
vi.mock('@/convex/_generated/api', () => ({
  api: {
    users: {
      findByEmail: vi.fn(),
      findById: vi.fn(),
      findByClerkId: vi.fn(),
      getCurrentUser: vi.fn(),
      listUsers: vi.fn(),
      searchUsers: vi.fn(),
      updateProfile: vi.fn(),
      findByRole: vi.fn(),
      findByDepartment: vi.fn(),
    },
  },
}));

describe('UserRepository', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('findByEmail', () => {
    it('should find user by email successfully', async () => {
      const mockUser = createMockUser({ email: '<EMAIL>' });
      (api.users.findByEmail as any).mockResolvedValue(mockUser);

      const result = await userRepository.findByEmail('<EMAIL>');

      expect(result).toEqual(mockUser);
      expect(api.users.findByEmail).toHaveBeenCalledWith({ email: '<EMAIL>' });
    });

    it('should return null when user not found', async () => {
      (api.users.findByEmail as any).mockResolvedValue(null);

      const result = await userRepository.findByEmail('<EMAIL>');

      expect(result).toBeNull();
    });

    it('should handle errors gracefully', async () => {
      const error = new Error('Database error');
      (api.users.findByEmail as any).mockRejectedValue(error);

      await expect(userRepository.findByEmail('<EMAIL>')).rejects.toThrow('Database error');
    });

    it('should handle edge cases with email validation', async () => {
      const testCases = [
        { email: '', expected: null },
        { email: 'invalid-email', expected: null },
        { email: 'test@', expected: null },
        { email: '@example.com', expected: null },
      ];

      for (const { email, expected } of testCases) {
        (api.users.findByEmail as any).mockResolvedValue(expected);
        const result = await userRepository.findByEmail(email);
        expect(result).toBe(expected);
      }
    });
  });

  describe('findById', () => {
    it('should find user by ID successfully', async () => {
      const mockUser = createMockUser({ _id: 'user123' as any });
      (api.users.findById as any).mockResolvedValue(mockUser);

      const result = await userRepository.findById('user123' as any);

      expect(result).toEqual(mockUser);
      expect(api.users.findById).toHaveBeenCalledWith({ userId: 'user123' });
    });

    it('should return null for non-existent ID', async () => {
      (api.users.findById as any).mockResolvedValue(null);

      const result = await userRepository.findById('nonexistent' as any);

      expect(result).toBeNull();
    });
  });

  describe('findByClerkId', () => {
    it('should find user by Clerk ID successfully', async () => {
      const mockUser = createMockUser({ clerkUserId: 'clerk123' });
      (api.users.findByClerkId as any).mockResolvedValue(mockUser);

      const result = await userRepository.findByClerkId('clerk123');

      expect(result).toEqual(mockUser);
      expect(api.users.findByClerkId).toHaveBeenCalledWith({ clerkUserId: 'clerk123' });
    });
  });

  describe('getAll', () => {
    it('should return all users with limit', async () => {
      const mockUsers = [
        createMockUser({ _id: 'user1' as any, name: 'User 1' }),
        createMockUser({ _id: 'user2' as any, name: 'User 2' }),
      ];
      (api.users.listUsers as any).mockResolvedValue(mockUsers);

      const result = await userRepository.getAll({ limit: 10 });

      expect(result).toEqual(mockUsers);
      expect(api.users.listUsers).toHaveBeenCalledWith({ limit: 10 });
    });

    it('should return all users without limit', async () => {
      const mockUsers = [createMockUser(), createMockUser()];
      (api.users.listUsers as any).mockResolvedValue(mockUsers);

      const result = await userRepository.getAll();

      expect(result).toEqual(mockUsers);
      expect(api.users.listUsers).toHaveBeenCalledWith({});
    });

    it('should handle empty results', async () => {
      (api.users.listUsers as any).mockResolvedValue([]);

      const result = await userRepository.getAll();

      expect(result).toEqual([]);
    });
  });

  describe('search', () => {
    it('should search users by query', async () => {
      const mockUsers = [
        createMockUser({ name: 'John Doe', email: '<EMAIL>' }),
        createMockUser({ name: 'Jane Smith', email: '<EMAIL>' }),
      ];
      (api.users.searchUsers as any).mockResolvedValue(mockUsers);

      const result = await userRepository.search('john');

      expect(result).toEqual(mockUsers);
      expect(api.users.searchUsers).toHaveBeenCalledWith({ query: 'john', limit: undefined });
    });

    it('should search with limit', async () => {
      const mockUsers = [createMockUser()];
      (api.users.searchUsers as any).mockResolvedValue(mockUsers);

      const result = await userRepository.search('test', { limit: 5 });

      expect(result).toEqual(mockUsers);
      expect(api.users.searchUsers).toHaveBeenCalledWith({ query: 'test', limit: 5 });
    });

    it('should handle empty search results', async () => {
      (api.users.searchUsers as any).mockResolvedValue([]);

      const result = await userRepository.search('nonexistent');

      expect(result).toEqual([]);
    });
  });

  describe('updateProfile', () => {
    it('should update user profile successfully', async () => {
      const userId = 'user123' as any;
      const updateData = { department: 'Engineering', phone: '+1234567890' };
      (api.users.updateProfile as any).mockResolvedValue(userId);

      const result = await userRepository.updateProfile(userId, updateData);

      expect(result).toBe(userId);
      expect(api.users.updateProfile).toHaveBeenCalledWith({
        userId,
        department: 'Engineering',
        phone: '+1234567890',
      });
    });

    it('should handle partial updates', async () => {
      const userId = 'user123' as any;
      const updateData = { department: 'HR' };
      (api.users.updateProfile as any).mockResolvedValue(userId);

      const result = await userRepository.updateProfile(userId, updateData);

      expect(result).toBe(userId);
      expect(api.users.updateProfile).toHaveBeenCalledWith({
        userId,
        department: 'HR',
      });
    });
  });

  describe('findByRole', () => {
    it('should find users by role', async () => {
      const mockUsers = [
        createMockUser({ role: 'Admin' }),
        createMockUser({ role: 'Admin' }),
      ];
      (api.users.findByRole as any).mockResolvedValue(mockUsers);

      const result = await userRepository.findByRole('Admin');

      expect(result).toEqual(mockUsers);
      expect(api.users.findByRole).toHaveBeenCalledWith({ role: 'Admin', limit: undefined });
    });

    it('should handle role with limit', async () => {
      const mockUsers = [createMockUser({ role: 'User' })];
      (api.users.findByRole as any).mockResolvedValue(mockUsers);

      const result = await userRepository.findByRole('User', { limit: 5 });

      expect(result).toEqual(mockUsers);
      expect(api.users.findByRole).toHaveBeenCalledWith({ role: 'User', limit: 5 });
    });
  });

  describe('findByDepartment', () => {
    it('should find users by department', async () => {
      const mockUsers = [
        createMockUser({ department: 'Engineering' }),
        createMockUser({ department: 'Engineering' }),
      ];
      (api.users.findByDepartment as any).mockResolvedValue(mockUsers);

      const result = await userRepository.findByDepartment('Engineering');

      expect(result).toEqual(mockUsers);
      expect(api.users.findByDepartment).toHaveBeenCalledWith({ department: 'Engineering', limit: undefined });
    });
  });

  describe('count', () => {
    it('should count total users', async () => {
      const mockUsers = [createMockUser(), createMockUser(), createMockUser()];
      (api.users.listUsers as any).mockResolvedValue(mockUsers);

      const result = await userRepository.count();

      expect(result).toBe(3);
    });

    it('should return 0 for empty users', async () => {
      (api.users.listUsers as any).mockResolvedValue([]);

      const result = await userRepository.count();

      expect(result).toBe(0);
    });

    it('should handle count errors', async () => {
      const error = new Error('Count error');
      (api.users.listUsers as any).mockRejectedValue(error);

      await expect(userRepository.count()).rejects.toThrow('Count error');
    });
  });

  describe('property-based testing', () => {
    it('should handle various email formats', async () => {
      const emailFormats = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      for (const email of emailFormats) {
        const mockUser = createMockUser({ email });
        (api.users.findByEmail as any).mockResolvedValue(mockUser);

        const result = await userRepository.findByEmail(email);
        expect(result?.email).toBe(email);
      }
    });

    it('should handle role variations', async () => {
      const roles = ['User', 'Admin', 'Manager', 'Supervisor', 'Guest'];
      
      for (const role of roles) {
        const mockUsers = [createMockUser({ role })];
        (api.users.findByRole as any).mockResolvedValue(mockUsers);

        const result = await userRepository.findByRole(role);
        expect(result[0].role).toBe(role);
      }
    });

    it('should handle department variations', async () => {
      const departments = ['IT', 'HR', 'Finance', 'Marketing', 'Operations'];
      
      for (const department of departments) {
        const mockUsers = [createMockUser({ department })];
        (api.users.findByDepartment as any).mockResolvedValue(mockUsers);

        const result = await userRepository.findByDepartment(department);
        expect(result[0].department).toBe(department);
      }
    });
  });

  describe('concurrency testing', () => {
    it('should handle concurrent find operations', async () => {
      const mockUser = createMockUser();
      (api.users.findByEmail as any).mockResolvedValue(mockUser);

      const promises = Array.from({ length: 10 }, () => 
        userRepository.findByEmail('<EMAIL>')
      );

      const results = await Promise.all(promises);
      results.forEach(result => {
        expect(result).toEqual(mockUser);
      });
    });

    it('should handle concurrent search operations', async () => {
      const mockUsers = [createMockUser(), createMockUser()];
      (api.users.searchUsers as any).mockResolvedValue(mockUsers);

      const promises = Array.from({ length: 5 }, () => 
        userRepository.search('test')
      );

      const results = await Promise.all(promises);
      results.forEach(result => {
        expect(result).toEqual(mockUsers);
      });
    });
  });

  describe('error handling', () => {
    it('should handle network errors gracefully', async () => {
      const networkError = new Error('Network error');
      (api.users.findByEmail as any).mockRejectedValue(networkError);

      await expect(userRepository.findByEmail('<EMAIL>'))
        .rejects.toThrow('Network error');
    });

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      (api.users.findByEmail as any).mockRejectedValue(timeoutError);

      await expect(userRepository.findByEmail('<EMAIL>'))
        .rejects.toThrow('Request timeout');
    });

    it('should handle validation errors', async () => {
      const validationError = new Error('Invalid email format');
      (api.users.findByEmail as any).mockRejectedValue(validationError);

      await expect(userRepository.findByEmail('invalid-email'))
        .rejects.toThrow('Invalid email format');
    });
  });
});