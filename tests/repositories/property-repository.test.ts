import { describe, it, expect, vi, beforeEach } from 'vitest';
import { propertyRepository } from '@/app/repositories/property-repository';
import { api } from '@/convex/_generated/api';
import { createMockProperty, createMockPropertyArea } from '../test-utils';

// Mock the Convex API
vi.mock('@/convex/_generated/api', () => ({
  api: {
    properties: {
      getProperty: vi.fn(),
      listProperties: vi.fn(),
      createProperty: vi.fn(),
      updateProperty: vi.fn(),
      deleteProperty: vi.fn(),
      searchProperties: vi.fn(),
      getPropertiesByManager: vi.fn(),
      getPropertiesByRegion: vi.fn(),
      getPropertiesByType: vi.fn(),
      getPropertiesByStatus: vi.fn(),
      createPropertyArea: vi.fn(),
      getPropertyAreas: vi.fn(),
      updatePropertyArea: vi.fn(),
      deletePropertyArea: vi.fn(),
    },
  },
}));

describe('PropertyRepository', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('findById', () => {
    it('should find property by ID successfully', async () => {
      const mockProperty = createMockProperty({ _id: 'prop123' as any });
      (api.properties.getProperty as any).mockResolvedValue(mockProperty);

      const result = await propertyRepository.findById('prop123' as any);

      expect(result).toEqual(mockProperty);
      expect(api.properties.getProperty).toHaveBeenCalledWith({ propertyId: 'prop123' });
    });

    it('should return null when property not found', async () => {
      (api.properties.getProperty as any).mockResolvedValue(null);

      const result = await propertyRepository.findById('nonexistent' as any);

      expect(result).toBeNull();
    });

    it('should handle errors gracefully', async () => {
      const error = new Error('Database error');
      (api.properties.getProperty as any).mockRejectedValue(error);

      await expect(propertyRepository.findById('test123' as any)).rejects.toThrow('Database error');
    });
  });

  describe('getAll', () => {
    it('should return all properties with limit', async () => {
      const mockProperties = [
        createMockProperty({ _id: 'prop1' as any, name: 'Property 1' }),
        createMockProperty({ _id: 'prop2' as any, name: 'Property 2' }),
      ];
      (api.properties.listProperties as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.getAll({ limit: 10 });

      expect(result).toEqual(mockProperties);
      expect(api.properties.listProperties).toHaveBeenCalledWith({ limit: 10 });
    });

    it('should return all properties without limit', async () => {
      const mockProperties = [createMockProperty(), createMockProperty()];
      (api.properties.listProperties as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.getAll();

      expect(result).toEqual(mockProperties);
      expect(api.properties.listProperties).toHaveBeenCalledWith({});
    });

    it('should handle empty results', async () => {
      (api.properties.listProperties as any).mockResolvedValue([]);

      const result = await propertyRepository.getAll();

      expect(result).toEqual([]);
    });
  });

  describe('create', () => {
    it('should create a new property successfully', async () => {
      const newProperty = {
        name: 'New Office Building',
        address: '123 Business St',
        suburb: 'Business District',
        state: 'NSW',
        postcode: '2000',
        region: 'Sydney',
        type: 'Office',
        status: 'Active',
        managerId: 'manager123' as any,
        contractValue: 500000,
      };
      const createdId = 'new-prop-id' as any;
      (api.properties.createProperty as any).mockResolvedValue(createdId);

      const result = await propertyRepository.create(newProperty);

      expect(result).toBe(createdId);
      expect(api.properties.createProperty).toHaveBeenCalledWith(newProperty);
    });

    it('should handle creation errors', async () => {
      const newProperty = {
        name: 'Test Property',
        address: '123 Test St',
        suburb: 'Testville',
        state: 'NSW',
        postcode: '2000',
        region: 'Sydney',
        type: 'Office',
        status: 'Active',
      };
      const error = new Error('Creation failed');
      (api.properties.createProperty as any).mockRejectedValue(error);

      await expect(propertyRepository.create(newProperty)).rejects.toThrow('Creation failed');
    });
  });

  describe('update', () => {
    it('should update a property successfully', async () => {
      const propertyId = 'prop123' as any;
      const updateData = { name: 'Updated Property Name', status: 'Inactive' };
      (api.properties.updateProperty as any).mockResolvedValue(propertyId);

      const result = await propertyRepository.update(propertyId, updateData);

      expect(result).toBe(propertyId);
      expect(api.properties.updateProperty).toHaveBeenCalledWith({
        propertyId,
        name: 'Updated Property Name',
        status: 'Inactive',
      });
    });

    it('should handle partial updates', async () => {
      const propertyId = 'prop123' as any;
      const updateData = { managerId: 'new-manager' as any };
      (api.properties.updateProperty as any).mockResolvedValue(propertyId);

      const result = await propertyRepository.update(propertyId, updateData);

      expect(result).toBe(propertyId);
      expect(api.properties.updateProperty).toHaveBeenCalledWith({
        propertyId,
        managerId: 'new-manager',
      });
    });
  });

  describe('delete', () => {
    it('should delete a property successfully', async () => {
      const propertyId = 'prop123' as any;
      (api.properties.deleteProperty as any).mockResolvedValue(propertyId);

      const result = await propertyRepository.delete(propertyId);

      expect(result).toBe(propertyId);
      expect(api.properties.deleteProperty).toHaveBeenCalledWith({ propertyId });
    });

    it('should handle deletion errors', async () => {
      const propertyId = 'prop123' as any;
      const error = new Error('Deletion failed');
      (api.properties.deleteProperty as any).mockRejectedValue(error);

      await expect(propertyRepository.delete(propertyId)).rejects.toThrow('Deletion failed');
    });
  });

  describe('search', () => {
    it('should search properties by query', async () => {
      const mockProperties = [
        createMockProperty({ name: 'Sydney Office', address: '123 Sydney St' }),
        createMockProperty({ name: 'Melbourne Office', address: '456 Melbourne Rd' }),
      ];
      (api.properties.searchProperties as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.search('office');

      expect(result).toEqual(mockProperties);
      expect(api.properties.searchProperties).toHaveBeenCalledWith({ query: 'office', limit: undefined });
    });

    it('should search with limit', async () => {
      const mockProperties = [createMockProperty()];
      (api.properties.searchProperties as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.search('test', { limit: 5 });

      expect(result).toEqual(mockProperties);
      expect(api.properties.searchProperties).toHaveBeenCalledWith({ query: 'test', limit: 5 });
    });

    it('should handle empty search results', async () => {
      (api.properties.searchProperties as any).mockResolvedValue([]);

      const result = await propertyRepository.search('nonexistent');

      expect(result).toEqual([]);
    });
  });

  describe('getByManager', () => {
    it('should get properties by manager', async () => {
      const mockProperties = [
        createMockProperty({ managerId: 'manager123' as any, name: 'Property 1' }),
        createMockProperty({ managerId: 'manager123' as any, name: 'Property 2' }),
      ];
      (api.properties.getPropertiesByManager as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.getByManager('manager123' as any);

      expect(result).toEqual(mockProperties);
      expect(api.properties.getPropertiesByManager).toHaveBeenCalledWith({ managerId: 'manager123', limit: undefined });
    });

    it('should handle manager with limit', async () => {
      const mockProperties = [createMockProperty({ managerId: 'manager123' as any })];
      (api.properties.getPropertiesByManager as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.getByManager('manager123' as any, { limit: 5 });

      expect(result).toEqual(mockProperties);
      expect(api.properties.getPropertiesByManager).toHaveBeenCalledWith({ managerId: 'manager123', limit: 5 });
    });
  });

  describe('getByRegion', () => {
    it('should get properties by region', async () => {
      const mockProperties = [
        createMockProperty({ region: 'Sydney', name: 'Sydney Office' }),
        createMockProperty({ region: 'Sydney', name: 'Sydney Warehouse' }),
      ];
      (api.properties.getPropertiesByRegion as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.getByRegion('Sydney');

      expect(result).toEqual(mockProperties);
      expect(api.properties.getPropertiesByRegion).toHaveBeenCalledWith({ region: 'Sydney', limit: undefined });
    });
  });

  describe('getByType', () => {
    it('should get properties by type', async () => {
      const mockProperties = [
        createMockProperty({ type: 'Office', name: 'Office Building 1' }),
        createMockProperty({ type: 'Office', name: 'Office Building 2' }),
      ];
      (api.properties.getPropertiesByType as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.getByType('Office');

      expect(result).toEqual(mockProperties);
      expect(api.properties.getPropertiesByType).toHaveBeenCalledWith({ type: 'Office', limit: undefined });
    });
  });

  describe('getByStatus', () => {
    it('should get properties by status', async () => {
      const mockProperties = [
        createMockProperty({ status: 'Active', name: 'Active Property 1' }),
        createMockProperty({ status: 'Active', name: 'Active Property 2' }),
      ];
      (api.properties.getPropertiesByStatus as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.getByStatus('Active');

      expect(result).toEqual(mockProperties);
      expect(api.properties.getPropertiesByStatus).toHaveBeenCalledWith({ status: 'Active', limit: undefined });
    });
  });

  describe('addArea', () => {
    it('should add an area to a property successfully', async () => {
      const newArea = {
        name: 'Conference Room',
        description: 'Main conference room',
        areaType: 'Meeting',
        size: 50,
        floor: 'Level 2',
        specialRequirements: ['Projector', 'Whiteboard'],
        propertyId: 'prop123' as any,
      };
      const createdId = 'new-area-id' as any;
      (api.properties.createPropertyArea as any).mockResolvedValue(createdId);

      const result = await propertyRepository.addArea(newArea);

      expect(result).toBe(createdId);
      expect(api.properties.createPropertyArea).toHaveBeenCalledWith(newArea);
    });
  });

  describe('getAreas', () => {
    it('should get areas for a property', async () => {
      const mockAreas = [
        createMockPropertyArea({ name: 'Area 1' }),
        createMockPropertyArea({ name: 'Area 2' }),
      ];
      (api.properties.getPropertyAreas as any).mockResolvedValue(mockAreas);

      const result = await propertyRepository.getAreas('prop123' as any);

      expect(result).toEqual(mockAreas);
      expect(api.properties.getPropertyAreas).toHaveBeenCalledWith({ propertyId: 'prop123' });
    });

    it('should handle empty areas', async () => {
      (api.properties.getPropertyAreas as any).mockResolvedValue([]);

      const result = await propertyRepository.getAreas('prop123' as any);

      expect(result).toEqual([]);
    });
  });

  describe('updateArea', () => {
    it('should update a property area successfully', async () => {
      const areaId = 'area123' as any;
      const updateData = { name: 'Updated Area Name', size: 75 };
      (api.properties.updatePropertyArea as any).mockResolvedValue(areaId);

      const result = await propertyRepository.updateArea(areaId, updateData);

      expect(result).toBe(areaId);
      expect(api.properties.updatePropertyArea).toHaveBeenCalledWith({
        areaId,
        name: 'Updated Area Name',
        size: 75,
      });
    });
  });

  describe('deleteArea', () => {
    it('should delete a property area successfully', async () => {
      const areaId = 'area123' as any;
      (api.properties.deletePropertyArea as any).mockResolvedValue(areaId);

      const result = await propertyRepository.deleteArea(areaId);

      expect(result).toBe(areaId);
      expect(api.properties.deletePropertyArea).toHaveBeenCalledWith({ areaId });
    });
  });

  describe('count', () => {
    it('should count total properties', async () => {
      const mockProperties = [createMockProperty(), createMockProperty(), createMockProperty()];
      (api.properties.listProperties as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.count();

      expect(result).toBe(3);
    });

    it('should return 0 for empty properties', async () => {
      (api.properties.listProperties as any).mockResolvedValue([]);

      const result = await propertyRepository.count();

      expect(result).toBe(0);
    });
  });

  describe('getStatsByRegion', () => {
    it('should return property statistics by region', async () => {
      const mockProperties = [
        createMockProperty({ region: 'Sydney' }),
        createMockProperty({ region: 'Sydney' }),
        createMockProperty({ region: 'Melbourne' }),
      ];
      (api.properties.listProperties as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.getStatsByRegion();

      expect(result).toEqual([
        { region: 'Sydney', count: 2 },
        { region: 'Melbourne', count: 1 },
      ]);
    });

    it('should handle empty properties for stats', async () => {
      (api.properties.listProperties as any).mockResolvedValue([]);

      const result = await propertyRepository.getStatsByRegion();

      expect(result).toEqual([]);
    });
  });

  describe('getStatsByType', () => {
    it('should return property statistics by type', async () => {
      const mockProperties = [
        createMockProperty({ type: 'Office' }),
        createMockProperty({ type: 'Office' }),
        createMockProperty({ type: 'Retail' }),
      ];
      (api.properties.listProperties as any).mockResolvedValue(mockProperties);

      const result = await propertyRepository.getStatsByType();

      expect(result).toEqual([
        { type: 'Office', count: 2 },
        { type: 'Retail', count: 1 },
      ]);
    });
  });

  describe('property-based testing', () => {
    it('should handle various property types', async () => {
      const propertyTypes = ['Office', 'Retail', 'Industrial', 'Residential', 'Warehouse'];
      
      for (const type of propertyTypes) {
        const mockProperties = [createMockProperty({ type })];
        (api.properties.getPropertiesByType as any).mockResolvedValue(mockProperties);

        const result = await propertyRepository.getByType(type);
        expect(result[0].type).toBe(type);
      }
    });

    it('should handle various regions', async () => {
      const regions = ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide'];
      
      for (const region of regions) {
        const mockProperties = [createMockProperty({ region })];
        (api.properties.getPropertiesByRegion as any).mockResolvedValue(mockProperties);

        const result = await propertyRepository.getByRegion(region);
        expect(result[0].region).toBe(region);
      }
    });

    it('should handle various statuses', async () => {
      const statuses = ['Active', 'Inactive', 'Pending', 'Archived'];
      
      for (const status of statuses) {
        const mockProperties = [createMockProperty({ status })];
        (api.properties.getPropertiesByStatus as any).mockResolvedValue(mockProperties);

        const result = await propertyRepository.getByStatus(status);
        expect(result[0].status).toBe(status);
      }
    });
  });

  describe('concurrency testing', () => {
    it('should handle concurrent find operations', async () => {
      const mockProperty = createMockProperty();
      (api.properties.getProperty as any).mockResolvedValue(mockProperty);

      const promises = Array.from({ length: 10 }, () => 
        propertyRepository.findById('prop123' as any)
      );

      const results = await Promise.all(promises);
      results.forEach(result => {
        expect(result).toEqual(mockProperty);
      });
    });

    it('should handle concurrent search operations', async () => {
      const mockProperties = [createMockProperty(), createMockProperty()];
      (api.properties.searchProperties as any).mockResolvedValue(mockProperties);

      const promises = Array.from({ length: 5 }, () => 
        propertyRepository.search('test')
      );

      const results = await Promise.all(promises);
      results.forEach(result => {
        expect(result).toEqual(mockProperties);
      });
    });
  });

  describe('error handling', () => {
    it('should handle network errors gracefully', async () => {
      const networkError = new Error('Network error');
      (api.properties.getProperty as any).mockRejectedValue(networkError);

      await expect(propertyRepository.findById('prop123' as any))
        .rejects.toThrow('Network error');
    });

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      (api.properties.getProperty as any).mockRejectedValue(timeoutError);

      await expect(propertyRepository.findById('prop123' as any))
        .rejects.toThrow('Request timeout');
    });

    it('should handle validation errors', async () => {
      const validationError = new Error('Invalid property ID');
      (api.properties.getProperty as any).mockRejectedValue(validationError);

      await expect(propertyRepository.findById('invalid-id' as any))
        .rejects.toThrow('Invalid property ID');
    });
  });
});