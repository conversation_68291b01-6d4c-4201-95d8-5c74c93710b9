"use client"

import React, { useState, useEffect } from 'react'
import { AppHeader } from '@/components/ui/app-header'
import { Button } from '@/components/ui/button'
import { useUser } from '@/contexts/user-context'
import { useRouter } from 'next/navigation'
import { useOrganization, useOrganizationList } from '@clerk/nextjs'
import { toast } from 'sonner'
import { CircleCheck, CircleX, UserCog, Users, Building, ArrowLeft, Settings, PlusCircle, Trash2 } from 'lucide-react'
import Image from 'next/image'

interface OrganizationMember {
  id: string
  userId: string
  role: string
  joinedAt: Date
  user: {
    id: string
    name: string
    email: string
    avatar: string | null
    department: string
  }
}

export function OrganizationSettingsScreen() {
  const { user, isLoading } = useUser()
  const { organization, isLoaded: isOrgLoaded } = useOrganization()
  const { userMemberships, isLoaded: isOrgListLoaded, createOrganization } = useOrganizationList()
  const [members, setMembers] = useState<OrganizationMember[]>([])
  const [isLoadingMembers, setIsLoadingMembers] = useState(true)
  const [newMemberEmail, setNewMemberEmail] = useState('')
  const [newMemberRole, setNewMemberRole] = useState('member')
  const [isAddingMember, setIsAddingMember] = useState(false)
  const [canManageMembers, setCanManageMembers] = useState(false)
  const router = useRouter()

  // Determine if user can manage members
  useEffect(() => {
    if (isOrgLoaded && organization) {
      // TODO: Check proper way to determine admin role in Clerk
      setCanManageMembers(true); // Temporarily allow all users to manage
    }
  }, [isOrgLoaded, organization])

  // Fetch organization members
  useEffect(() => {
    const fetchMembers = async () => {
      if (!isOrgLoaded || !organization) return;
      
      setIsLoadingMembers(true);
      try {
        const response = await fetch('/api/organizations/members');
        if (!response.ok) throw new Error('Failed to fetch members');
        
        const data = await response.json();
        setMembers(data.members || []);
      } catch (error) {
        console.error('Error fetching members:', error);
        toast.error('Failed to load organization members');
      } finally {
        setIsLoadingMembers(false);
      }
    };

    fetchMembers();
  }, [isOrgLoaded, organization]);

  const handleBack = () => {
    router.back();
  };

  const handleAddMember = async () => {
    if (!newMemberEmail.trim()) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsAddingMember(true);
    
    try {
      // This would typically involve:
      // 1. Finding or creating the user by email
      // 2. Adding them to the Clerk organization
      // 3. The webhook would handle adding them to our database
      
      // For demo, we'll show a toast
      toast.success(`Invitation sent to ${newMemberEmail}`);
      setNewMemberEmail('');
      setNewMemberRole('member');
      
      // Refresh members list
      const response = await fetch('/api/organizations/members');
      if (response.ok) {
        const data = await response.json();
        setMembers(data.members || []);
      }
    } catch (error) {
      console.error('Error adding member:', error);
      toast.error('Failed to add member');
    } finally {
      setIsAddingMember(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (!confirm('Are you sure you want to remove this member?')) {
      return;
    }

    try {
      const response = await fetch(`/api/organizations/members/${memberId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to remove member');
      
      // Update local members state
      setMembers(members.filter(m => m.userId !== memberId));
      toast.success('Member removed successfully');
    } catch (error) {
      console.error('Error removing member:', error);
      toast.error('Failed to remove member');
    }
  };

  const handleUpdateRole = async (memberId: string, newRole: string) => {
    try {
      const response = await fetch(`/api/organizations/members/${memberId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role: newRole }),
      });

      if (!response.ok) throw new Error('Failed to update role');
      
      // Update local members state
      setMembers(members.map(m => 
        m.userId === memberId ? { ...m, role: newRole } : m
      ));
      
      toast.success('Role updated successfully');
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error('Failed to update role');
    }
  };

  const handleCreateOrganization = async () => {
    try {
      if (createOrganization) {
        await createOrganization({ name: 'New Organization' });
      } else {
        throw new Error('createOrganization is not available');
      }
      toast.success('New organization created');
      router.refresh();
    } catch (error) {
      console.error('Error creating organization:', error);
      toast.error('Failed to create organization');
    }
  };

  const handleMigrateData = async () => {
    try {
      const response = await fetch('/api/organizations/migrate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          migrateProperties: true,
          migrateContracts: true,
        }),
      });

      if (!response.ok) throw new Error('Failed to migrate data');
      
      toast.success('Data migration completed successfully');
    } catch (error) {
      console.error('Error migrating data:', error);
      toast.error('Failed to migrate data');
    }
  };

  if (isLoading || !isOrgLoaded) {
    return (
      <div className="flex flex-col min-h-screen bg-black text-white">
        <AppHeader />
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-pulse">Loading...</div>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="flex flex-col min-h-screen bg-black text-white">
        <AppHeader />
        <div className="flex-1 flex flex-col items-center justify-center p-4">
          <div className="mb-8 text-center">
            <Building className="w-16 h-16 text-[#A4D321] mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-2">No Organization Selected</h1>
            <p className="text-zinc-400 mb-6">Create or join an organization to access this page</p>
            
            <Button 
              onClick={handleCreateOrganization}
              className="bg-[#A4D321] hover:bg-[#BFE550] text-black font-medium px-4 py-2 rounded-full flex items-center"
            >
              <PlusCircle className="w-4 h-4 mr-2" />
              Create Organization
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-black text-white pb-20">
      <AppHeader />
      
      <div className="container mx-auto px-4 py-6 mt-20">
        <Button
          variant="ghost"
          className="mb-6 text-white"
          onClick={handleBack}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>

        <div className="flex items-center mb-8">
          {organization.imageUrl ? (
            <Image 
              src={organization.imageUrl} 
              alt={organization.name || 'Organization'}
              width={64}
              height={64}
              className="w-16 h-16 rounded-full mr-4"
            />
          ) : (
            <div className="w-16 h-16 rounded-full bg-[#A4D321] flex items-center justify-center mr-4">
              <Building className="w-8 h-8 text-black" />
            </div>
          )}
          
          <div>
            <h1 className="text-2xl font-bold">{organization.name}</h1>
            <p className="text-zinc-400">{organization.slug}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Organization Info */}
          <div className="col-span-1 md:col-span-2 space-y-6">
            <div className="bg-zinc-900 rounded-xl p-6 border border-zinc-800">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Settings className="w-5 h-5 mr-2 text-[#A4D321]" />
                Organization Settings
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-zinc-400 mb-1">
                    Organization ID
                  </label>
                  <div className="text-sm bg-zinc-800 p-2 rounded border border-zinc-700 font-mono">
                    {organization.id}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-zinc-400 mb-1">
                    Your Role
                  </label>
                  <div className="inline-flex items-center bg-zinc-800 px-2 py-1 rounded-full text-sm">
                    <UserCog className="w-3 h-3 mr-1 text-[#A4D321]" />
                    Member
                  </div>
                </div>
                
                {canManageMembers && (
                  <div className="pt-4 border-t border-zinc-800">
                    <h3 className="text-md font-medium mb-2">Data Migration</h3>
                    <p className="text-sm text-zinc-400 mb-3">
                      Migrate existing properties and contracts to this organization
                    </p>
                    <Button
                      onClick={handleMigrateData}
                      className="bg-zinc-800 hover:bg-zinc-700 text-white"
                    >
                      Migrate Data
                    </Button>
                  </div>
                )}
              </div>
            </div>
            
            <div className="bg-zinc-900 rounded-xl p-6 border border-zinc-800">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Users className="w-5 h-5 mr-2 text-[#A4D321]" />
                Members
                <span className="ml-2 text-sm bg-zinc-800 px-2 py-0.5 rounded-full">
                  {members.length}
                </span>
              </h2>
              
              {isLoadingMembers ? (
                <div className="text-center py-8">
                  <div className="animate-pulse">Loading members...</div>
                </div>
              ) : (
                <>
                  {canManageMembers && (
                    <div className="mb-6 p-4 bg-zinc-800 rounded-lg">
                      <h3 className="text-md font-medium mb-2">Add New Member</h3>
                      <div className="flex flex-col md:flex-row gap-2">
                        <input
                          type="email"
                          placeholder="Email address"
                          value={newMemberEmail}
                          onChange={(e) => setNewMemberEmail(e.target.value)}
                          className="flex-1 bg-zinc-700 border border-zinc-600 rounded px-3 py-2 focus:outline-none focus:ring-1 focus:ring-[#A4D321]"
                        />
                        <select
                          value={newMemberRole}
                          onChange={(e) => setNewMemberRole(e.target.value)}
                          className="bg-zinc-700 border border-zinc-600 rounded px-3 py-2 focus:outline-none focus:ring-1 focus:ring-[#A4D321]"
                        >
                          <option value="admin">Admin</option>
                          <option value="member">Member</option>
                        </select>
                        <Button
                          onClick={handleAddMember}
                          disabled={isAddingMember}
                          className="bg-[#A4D321] hover:bg-[#BFE550] text-black"
                        >
                          {isAddingMember ? 'Adding...' : 'Add Member'}
                        </Button>
                      </div>
                    </div>
                  )}
                  
                  <div className="space-y-3">
                    {members.map((member) => (
                      <div 
                        key={member.userId}
                        className="flex items-center justify-between p-3 bg-zinc-800 rounded-lg"
                      >
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded-full mr-3 flex items-center justify-center overflow-hidden">
                            {member.user.avatar ? (
                              <Image 
                                src={member.user.avatar} 
                                alt={member.user.name} 
                                width={40} 
                                height={40}
                                className="object-cover w-full h-full"
                              />
                            ) : (
                              <div className="w-full h-full bg-[#A4D321] flex items-center justify-center text-black font-bold">
                                {member.user.name.charAt(0)}
                              </div>
                            )}
                          </div>
                          <div>
                            <div className="font-medium">{member.user.name}</div>
                            <div className="text-xs text-zinc-400">{member.user.email}</div>
                          </div>
                        </div>
                        
                        <div className="flex items-center">
                          {canManageMembers && member.userId !== user?.id ? (
                            <>
                              <select
                                value={member.role}
                                onChange={(e) => handleUpdateRole(member.userId, e.target.value)}
                                className="mr-2 bg-zinc-700 border border-zinc-600 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-[#A4D321]"
                              >
                                <option value="admin">Admin</option>
                                <option value="member">Member</option>
                              </select>
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveMember(member.userId)}
                                className="text-red-500 hover:text-red-400 hover:bg-red-500/10"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </>
                          ) : (
                            <span className="inline-flex items-center bg-zinc-700 px-2 py-1 rounded text-xs">
                              {member.role === 'admin' ? (
                                <>
                                  <UserCog className="w-3 h-3 mr-1 text-[#A4D321]" />
                                  Admin
                                </>
                              ) : (
                                <>
                                  <Users className="w-3 h-3 mr-1 text-[#A4D321]" />
                                  Member
                                </>
                              )}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                    
                    {members.length === 0 && (
                      <div className="text-center py-8 text-zinc-400">
                        No members found
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
          
          {/* Your Organizations */}
          <div className="col-span-1">
            <div className="bg-zinc-900 rounded-xl p-6 border border-zinc-800">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Building className="w-5 h-5 mr-2 text-[#A4D321]" />
                Your Organizations
              </h2>
              
              {!isOrgListLoaded ? (
                <div className="text-center py-4">
                  <div className="animate-pulse">Loading...</div>
                </div>
              ) : (
                <>
                  <div className="space-y-3 mb-4">
                    {userMemberships?.data?.map((membership) => (
                      <div 
                        key={membership.organization.id} 
                        className={`flex items-center justify-between p-3 rounded-lg ${
                          membership.organization.id === organization.id 
                            ? 'bg-[#A4D321]/20 border border-[#A4D321]/30' 
                            : 'bg-zinc-800'
                        }`}
                      >
                        <div className="flex items-center">
                          {membership.organization.imageUrl ? (
                            <Image 
                              src={membership.organization.imageUrl} 
                              alt={membership.organization.name || 'Organization'} 
                              width={32} 
                              height={32}
                              className="w-8 h-8 rounded-full mr-3"
                            />
                          ) : (
                            <div className="w-8 h-8 rounded-full bg-zinc-700 flex items-center justify-center mr-3">
                              <Building className="w-4 h-4 text-zinc-300" />
                            </div>
                          )}
                          <div className="font-medium">{membership.organization.name}</div>
                        </div>
                        
                        {membership.organization.id === organization.id ? (
                          <CircleCheck className="w-5 h-5 text-[#A4D321]" />
                        ) : (
                          <button 
                            onClick={() => window.location.href = `/organizations/${membership.organization.slug}`}
                            className="text-sm text-zinc-400 hover:text-white"
                          >
                            Switch
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                  
                  <Button
                    onClick={handleCreateOrganization}
                    className="w-full bg-zinc-800 hover:bg-zinc-700 text-white"
                  >
                    <PlusCircle className="w-4 h-4 mr-2" />
                    Create Organization
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
