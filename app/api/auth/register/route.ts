import { NextRequest, NextResponse } from 'next/server';
import { authService, apiService } from '@/app/services';

interface RegisterBody {
  name?: string;
  email?: string;
  password?: string;
  role?: string;
  department?: string;
  phone?: string;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  return apiService.handlePublicRequest(req, async (data: unknown) => {
    const { name, email, password, role, department, phone } = data as RegisterBody;
    
    if (!name || !email || !password) {
      throw new Error('Name, email, and password are required');
    }
    
    const user = await authService.register({
      name,
      email,
      password,
      ...(role && { role }),
      ...(department && { department }),
      ...(phone && { phone }),
    });
    
    if (!user) {
      throw new Error('Failed to register user');
    }
    
    // Remove password from response
    const { password: _password, ...userWithoutPassword } = user;
    
    return { message: 'Registration successful', user: userWithoutPassword };
  });
}
