import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Define types based on Convex schema
export interface Message {
  _id: Id<"messages">;
  sessionId: Id<"chatSessions">;
  senderId: Id<"users">;
  content: string;
  contentType: string;
  metadata?: any;
  organizationId: Id<"organizations">;
  _creationTime: number;
}

export interface NewMessage {
  sessionId: Id<"chatSessions">;
  content: string;
  contentType: string;
  metadata?: any;
}

/**
 * Repository for message-related database operations
 * Wraps Convex queries and mutations with repository pattern
 */
export class MessageRepository {
  /**
   * Find a message by ID
   */
  async findById(id: Id<"messages">): Promise<Message | null> {
    try {
      // Messages are handled through chat session repository
      throw new Error("Use ChatSessionRepository.getMessages() instead");
    } catch (error) {
      console.error('Error finding message by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new message
   */
  async create(messageData: NewMessage): Promise<Id<"messages">> {
    try {
      const result = await convex.mutation(api.chat.sendMessage, messageData);
      return result;
    } catch (error) {
      console.error('Error creating message:', error);
      throw error;
    }
  }

  /**
   * Get messages for a session
   */
  async getBySession(sessionId: Id<"chatSessions">, limit?: number): Promise<any[]> {
    try {
      const queryOptions: { sessionId: Id<"chatSessions">; limit?: number } = { sessionId };
      if (limit !== undefined) {
        queryOptions.limit = limit;
      }
      const result = await convex.query(api.chat.getMessages, queryOptions);
      return result;
    } catch (error) {
      console.error('Error getting messages by session:', error);
      throw error;
    }
  }

  /**
   * Delete a message
   */
  async delete(id: Id<"messages">): Promise<Id<"messages">> {
    try {
      // TODO: Implement message deletion in Convex
      throw new Error("Not implemented - requires Convex functions");
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const messageRepository = new MessageRepository();