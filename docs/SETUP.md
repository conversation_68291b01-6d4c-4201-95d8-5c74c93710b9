# ARA Property Services - Setup Guide

Complete setup guide for new developers joining the project.

## Prerequisites

- **Node.js**: 18.x or later
- **Bun**: 1.0 or later (package manager and runtime)
- **Git**: Latest version
- **Clerk Account**: With organizations enabled
- **Convex Account**: For backend services

## Quick Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/danmarauda/askara-prod-final.git
   cd askara-prod-final
   ```

2. **Install dependencies**:
   ```bash
   bun install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your actual values
   ```

4. **Start development server**:
   ```bash
   bun dev
   ```

The application will be available at http://localhost:3000.

## Environment Configuration

### Required Environment Variables

Copy `.env.example` to `.env.local` and configure:

#### Core Application
```env
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

#### Convex Backend
```env
NEXT_PUBLIC_CONVEX_URL=https://your-project.convex.cloud
CONVEX_DEPLOYMENT=dev:your-deployment-name
```

#### Clerk Authentication
```env
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_key
CLERK_SECRET_KEY=sk_test_your_key
CLERK_WEBHOOK_SECRET=whsec_your_secret
```

#### AI Services (Optional)
```env
OPENAI_API_KEY=sk-proj-your_key
ELEVENLABS_API_KEY=sk_your_key
```

### Getting API Keys

1. **Convex**:
   - Sign up at [convex.dev](https://convex.dev)
   - Create a new project
   - Copy the deployment URL and name

2. **Clerk**:
   - Sign up at [clerk.com](https://clerk.com)
   - Create a new application
   - Enable organizations in settings
   - Copy the publishable key and secret key

3. **OpenAI** (for chat features):
   - Sign up at [platform.openai.com](https://platform.openai.com)
   - Create an API key

4. **ElevenLabs** (for voice features):
   - Sign up at [elevenlabs.io](https://elevenlabs.io)
   - Create an API key

## Development Workflow

### Available Scripts

```bash
# Development
bun dev               # Start development server
bun run build         # Build for production
bun start             # Start production server
bun run lint          # Run ESLint

# Testing
bun test              # Run tests
bun run test:clerk    # Test Clerk integration

# Database (Legacy)
bun run db:push       # Push schema to database
bun run db:studio     # Open Drizzle Studio
```

### Project Structure

```
askara-prod-final/
├── app/              # Next.js app directory
├── components/       # React components
├── convex/          # Convex backend functions
├── lib/             # Utility functions
├── hooks/           # Custom React hooks
├── contexts/        # React contexts
├── docs/            # Documentation
└── public/          # Static assets
```

## Troubleshooting

### Common Issues

1. **Port already in use**:
   ```bash
   # Kill process on port 3000
   lsof -ti:3000 | xargs kill -9
   ```

2. **Dependencies issues**:
   ```bash
   # Clean install
   rm -rf node_modules bun.lockb
   bun install
   ```

3. **Environment variables not loading**:
   - Ensure `.env.local` is in the root directory
   - Restart the development server
   - Check for syntax errors in the file

### Getting Help

- Check the [TROUBLESHOOTING.md](./TROUBLESHOOTING.md) guide
- Review the relevant documentation files
- Contact the development team

## Next Steps

After setup:
1. Review the [DEVELOPMENT.md](./DEVELOPMENT.md) guide
2. Understand the [ARCHITECTURE.md](./ARCHITECTURE.md)
3. Check the [DATABASE.md](./DATABASE.md) for data management
4. See [DEPLOYMENT.md](./DEPLOYMENT.md) for deployment process
