import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Define types based on Convex schema
export interface Inspection {
  _id: Id<"inspectionReports">;
  propertyId: Id<"properties">;
  templateId?: Id<"inspectionTemplates">;
  inspectorId: Id<"users">;
  scheduledDate: number;
  completedDate?: number;
  status: string;
  score?: number;
  notes?: string;
  data: any;
  organizationId: Id<"organizations">;
  _creationTime: number;
}

export interface NewInspection {
  propertyId: Id<"properties">;
  templateId?: Id<"inspectionTemplates">;
  scheduledDate: number;
  status?: string;
  notes?: string;
  data?: any;
}

/**
 * Repository for inspection-related database operations
 * Wraps Convex queries and mutations with repository pattern
 */
export class InspectionRepository {
  /**
   * Find an inspection by ID
   */
  async findById(id: Id<"inspectionReports">): Promise<Inspection | null> {
    // TODO: Implement getInspectionReport query in Convex
    console.warn('getInspection not implemented in Convex API');
    return null;
  }

  /**
   * Get all inspections in current organization
   */
  async getAll(options?: { 
    propertyId?: Id<"properties">;
    status?: string;
    limit?: number;
  }): Promise<Inspection[]> {
    try {
      const queryOptions: {
        propertyId?: Id<"properties">;
        status?: string;
        inspectorId?: Id<"users">;
        startDate?: number;
        endDate?: number;
      } = {};
      
      if (options?.propertyId) {
        queryOptions.propertyId = options.propertyId;
      }
      if (options?.status) {
        queryOptions.status = options.status;
      }
      
      const result = await convex.query(api.inspections.listInspectionReports, queryOptions);
      return result;
    } catch (error) {
      console.error('Error getting all inspections:', error);
      throw error;
    }
  }

  /**
   * Create a new inspection
   */
  async create(inspectionData: NewInspection): Promise<Id<"inspectionReports">> {
    try {
      const result = await convex.mutation(api.inspections.createInspectionReport, inspectionData);
      return result;
    } catch (error) {
      console.error('Error creating inspection:', error);
      throw error;
    }
  }

  /**
   * Update an inspection
   */
  async update(id: Id<"inspectionReports">, inspectionData: Partial<{
    scheduledDate: number;
    completedDate?: number;
    status: string;
    score?: number;
    notes?: string;
  }>): Promise<Id<"inspectionReports">> {
    try {
      const result = await convex.mutation(api.inspections.updateInspectionReport, {
        reportId: id,
        ...inspectionData
      });
      return result;
    } catch (error) {
      console.error('Error updating inspection:', error);
      throw error;
    }
  }

  /**
   * Delete an inspection
   */
  async delete(id: Id<"inspectionReports">): Promise<Id<"inspectionReports">> {
    // TODO: Implement deleteInspectionReport mutation in Convex
    console.warn('deleteInspection not implemented in Convex API');
    return id;
  }

  /**
   * Get inspections by property
   */
  async getByProperty(propertyId: Id<"properties">, options?: { limit?: number }): Promise<Inspection[]> {
    try {
      const result = await convex.query(api.inspections.listInspectionReports, {
        propertyId
      });
      return result;
    } catch (error) {
      console.error('Error getting inspections by property:', error);
      throw error;
    }
  }

  /**
   * Get inspections by status
   */
  async getByStatus(status: string, options?: { limit?: number }): Promise<Inspection[]> {
    try {
      const result = await convex.query(api.inspections.listInspectionReports, {
        status
      });
      return result;
    } catch (error) {
      console.error('Error getting inspections by status:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const inspectionRepository = new InspectionRepository();