import { v } from "convex/values";
import { mutation, query, internalMutation } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Create or update user from Clerk webhook (internal)
export const upsertUser = internalMutation({
  args: {
    clerkUserId: v.string(),
    email: v.string(),
    name: v.string(),
    imageUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (existingUser) {
      // Update existing user
      await ctx.db.patch(existingUser._id, {
        name: args.name,
        email: args.email,
        avatar: args.imageUrl,
        lastLogin: Date.now(),
      });
      return existingUser._id;
    } else {
      // Create new user with default values
      const userId = await ctx.db.insert("users", {
        clerkUserId: args.clerkUserId,
        email: args.email,
        name: args.name,
        avatar: args.imageUrl,
        role: "staff", // Default role
        department: "Operations", // Default department
        lastLogin: Date.now(),
      });
      return userId;
    }
  },
});

// Get current user
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user) {
      return null;
    }

    // Get user's organization
    const orgMembership = await ctx.db
      .query("organizationMembers")
      .withIndex("by_user", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    let organization = null;
    if (orgMembership) {
      organization = await ctx.db.get(orgMembership.organizationId);
    }

    return {
      ...user,
      organization,
    };
  },
});

// Get user by ID
export const getUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) {
      return null;
    }

    // Get user's organization
    const orgMembership = await ctx.db
      .query("organizationMembers")
      .withIndex("by_user", (q) => q.eq("clerkUserId", user.clerkUserId))
      .first();

    let organization = null;
    if (orgMembership) {
      organization = await ctx.db.get(orgMembership.organizationId);
    }

    return {
      ...user,
      organization,
    };
  },
});

// List users in organization
export const listOrganizationUsers = query({
  args: { organizationId: v.id("organizations") },
  handler: async (ctx, args) => {
    // Get all members of the organization
    const members = await ctx.db
      .query("organizationMembers")
      .withIndex("by_org", (q) => q.eq("organizationId", args.organizationId))
      .collect();

    // Get user details for each member
    const users = await Promise.all(
      members.map(async (member) => {
        const user = await ctx.db
          .query("users")
          .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", member.clerkUserId))
          .first();
        
        if (!user) return null;
        
        return {
          ...user,
          organizationRole: member.role,
        };
      })
    );

    return users.filter(Boolean);
  },
});

// Update user profile
export const updateProfile = mutation({
  args: {
    userId: v.id("users"),
    name: v.optional(v.string()),
    phone: v.optional(v.string()),
    department: v.optional(v.string()),
    preferences: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify user can update this profile
    const user = await ctx.db.get(args.userId);
    if (!user || user.clerkUserId !== identity.subject) {
      throw new Error("Unauthorized");
    }

    const { userId, ...updates } = args;
    await ctx.db.patch(userId, updates);
    
    return userId;
  },
});

// Update user role (admin only)
export const updateUserRole = mutation({
  args: {
    userId: v.id("users"),
    role: v.string(),
    organizationId: v.id("organizations"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if current user is admin
    const currentUserMembership = await ctx.db
      .query("organizationMembers")
      .withIndex("by_user", (q) => q.eq("clerkUserId", identity.subject))
      .filter((q) => q.eq(q.field("organizationId"), args.organizationId))
      .first();

    if (!currentUserMembership || currentUserMembership.role !== "admin") {
      throw new Error("Only admins can update user roles");
    }

    // Update user role
    await ctx.db.patch(args.userId, { role: args.role });
    
    return args.userId;
  },
});

// Find user by email
export const findByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
    
    return user || null;
  },
});

// Find user by Convex ID
export const findById = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    return user || null;
  },
});

// Find user by Clerk ID
export const findByClerkId = query({
  args: { clerkUserId: v.string() },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();
    
    return user || null;
  },
});

// List users in current organization
export const listUsers = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    let query = ctx.db
      .query("users")
      .withIndex("by_org", (q) => q.eq("organizationId", user.organizationId!))
      .order("asc");

    const users = await query.collect();
    return args.limit ? users.slice(0, args.limit) : users;
  },
});

// Search users by name, email, or department
export const searchUsers = query({
  args: { 
    query: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    const users = await ctx.db
      .query("users")
      .withIndex("by_org", (q) => q.eq("organizationId", user.organizationId!))
      .collect();

    // Filter users based on search query
    const searchTerm = args.query.toLowerCase();
    const filteredUsers = users.filter(u => 
      u.name.toLowerCase().includes(searchTerm) ||
      u.email.toLowerCase().includes(searchTerm) ||
      (u.department && u.department.toLowerCase().includes(searchTerm))
    );

    return args.limit ? filteredUsers.slice(0, args.limit) : filteredUsers;
  },
});

// Find users by role
export const findByRole = query({
  args: { 
    role: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    let query = ctx.db
      .query("users")
      .withIndex("by_org", (q) => q.eq("organizationId", user.organizationId!))
      .filter((q) => q.eq(q.field("role"), args.role));

    const users = await query.collect();
    return args.limit ? users.slice(0, args.limit) : users;
  },
});

// Find users by department
export const findByDepartment = query({
  args: { 
    department: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    // Get user's organization
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", identity.subject))
      .first();

    if (!user || !user.organizationId) {
      return [];
    }

    let query = ctx.db
      .query("users")
      .withIndex("by_org", (q) => q.eq("organizationId", user.organizationId!))
      .filter((q) => q.eq(q.field("department"), args.department));

    const users = await query.collect();
    return args.limit ? users.slice(0, args.limit) : users;
  },
});