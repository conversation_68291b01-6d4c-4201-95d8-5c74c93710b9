{"buildCommand": "bun run build", "devCommand": "bun dev", "installCommand": "bun install", "framework": "nextjs", "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}, "regions": ["iad1"], "cleanUrls": true, "trailingSlash": false, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains"}]}], "rewrites": [{"source": "/(.*)", "destination": "/"}]}