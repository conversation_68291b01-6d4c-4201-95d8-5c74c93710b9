# ARA Property Services - Development Guide

Development workflow, best practices, and guidelines for the ARA Property Services application.

## Development Workflow

### Package Manager
This project uses **B<PERSON>** as the package manager and runtime with Turbopack for faster builds.

### Available Scripts

#### Core Development
```bash
bun dev               # Start development server with Turbopack
bun run build         # Build the application
bun start             # Start production server
bun run lint          # Run ESLint
bun test              # Run tests with <PERSON><PERSON><PERSON>
```

#### Database Operations (Legacy)
```bash
bun run db:push       # Push schema changes to database
bun run db:studio     # Open Drizzle Studio
bun run db:generate   # Generate database schema
bun run db:migrate    # Run database migrations
bun run db:seed       # Seed the database
```

#### Testing & Validation
```bash
bun run test:clerk    # Test Clerk integration
bun run test:neon     # Test Neon database connection
bun run validate:env  # Validate environment variables
bun run validate:all  # Run all validations
```

#### Maintenance
```bash
bun run clean         # Remove node_modules, .next, .turbo, dist
bun run clean:cache   # Clear bun cache
bun run reset         # Complete reset (runs reset-bun.sh)
```

## Project Structure

```
askara-prod-final/
├── app/                    # Next.js 15 app directory
│   ├── api/               # API routes
│   ├── (auth)/            # Authentication pages
│   ├── organization/      # Organization management
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   ├── auth/             # Authentication components
│   ├── chat/             # Chat interface
│   └── voice-assistant/  # Voice assistant components
├── convex/               # Convex backend functions
│   ├── schema.ts         # Database schema
│   ├── auth.config.ts    # Auth configuration
│   └── *.ts             # Function definitions
├── lib/                  # Utility functions
├── hooks/                # Custom React hooks
├── contexts/             # React contexts
├── types/                # TypeScript type definitions
├── docs/                 # Documentation
└── public/               # Static assets
```

## Technology Stack

### Frontend
- **Framework**: Next.js 15 with App Router
- **React**: Version 19
- **Styling**: Tailwind CSS with custom theme
- **UI Components**: Radix UI primitives
- **Animation**: Framer Motion
- **State Management**: React Context API

### Backend
- **Database**: Convex (real-time database)
- **Authentication**: Clerk with organization support
- **API**: Convex functions (replacing REST API)

### AI Integration
- **Chat**: OpenAI GPT models
- **Voice**: ElevenLabs for voice synthesis
- **Context**: Custom AI assistant implementation

### Development Tools
- **TypeScript**: Full type safety
- **ESLint**: Code linting
- **Prettier**: Code formatting (via ESLint)
- **Vitest**: Testing framework
- **Bun**: Package management and runtime

## Development Best Practices

### Code Organization
1. **Components**: Use functional components with hooks
2. **Types**: Define TypeScript interfaces for all data structures
3. **Utilities**: Place reusable functions in `lib/`
4. **Hooks**: Custom hooks in `hooks/` directory
5. **Contexts**: Global state in `contexts/` directory

### Naming Conventions
- **Files**: kebab-case for files and directories
- **Components**: PascalCase for React components
- **Functions**: camelCase for functions and variables
- **Constants**: UPPER_SNAKE_CASE for constants

### Git Workflow
1. Create feature branches from `main`
2. Use descriptive commit messages
3. Test thoroughly before pushing
4. Create pull requests for review
5. Squash commits when merging

### Environment Management
- Use `.env.local` for local development
- Never commit sensitive credentials
- Use `.env.example` as a template
- Validate environment variables on startup

## Multi-Tenant Architecture

The application supports multiple organizations with data isolation:

### Organization Structure
- Users belong to one or more organizations
- Data is scoped by organization ID
- Clerk handles organization management
- Convex functions enforce data isolation

### Development Considerations
- Always filter queries by organization
- Test with multiple organizations
- Ensure proper access controls
- Validate organization membership

## Testing Strategy

### Unit Tests
- Test utility functions in `lib/`
- Test custom hooks
- Test component logic

### Integration Tests
- Test API endpoints
- Test authentication flows
- Test database operations

### E2E Tests
- Test complete user workflows
- Test multi-tenant scenarios
- Test voice and chat features

## Performance Optimization

### Build Optimization
- Use Turbopack for faster development builds
- Optimize bundle size with tree shaking
- Use dynamic imports for code splitting

### Runtime Optimization
- Implement proper caching strategies
- Use React.memo for expensive components
- Optimize database queries
- Use Convex real-time subscriptions efficiently

## Troubleshooting

### Common Issues
1. **Build Errors**: Check TypeScript errors and dependencies
2. **Authentication Issues**: Verify Clerk configuration
3. **Database Issues**: Check Convex connection and schema
4. **Performance Issues**: Profile with React DevTools

### Debug Tools
- React DevTools
- Convex Dashboard
- Clerk Dashboard
- Browser DevTools

## Contributing Guidelines

1. Follow the established code style
2. Write tests for new features
3. Update documentation
4. Ensure accessibility compliance
5. Test on mobile devices
6. Validate multi-tenant functionality
