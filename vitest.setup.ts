// vitest.setup.ts
import '@testing-library/jest-dom';
import { vi, afterEach } from 'vitest';

// Mock environment variables
vi.stubEnv('NEXT_PUBLIC_CONVEX_URL', 'https://mock-convex-url.com');
vi.stubEnv('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY', 'mock-clerk-key');
vi.stubEnv('CLERK_SECRET_KEY', 'mock-clerk-secret');
vi.stubEnv('DATABASE_URL', 'postgresql://mock:mock@localhost:5432/test');
vi.stubEnv('NODE_ENV', 'test');

// Mock Next.js modules
vi.mock('next/navigation', () => ({
  redirect: vi.fn(),
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
    getAll: vi.fn(),
    has: vi.fn(),
    entries: vi.fn(),
    keys: vi.fn(),
    values: vi.fn(),
  }),
  usePathname: vi.fn(),
}));

vi.mock('next/headers', () => ({
  cookies: () => ({
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
    getAll: vi.fn(),
  }),
}));

vi.mock('next/cache', () => ({
  revalidatePath: vi.fn(),
  revalidateTag: vi.fn(),
}));

// Mock Convex
vi.mock('convex/browser', () => ({
  ConvexHttpClient: vi.fn().mockImplementation(() => ({
    query: vi.fn(),
    mutation: vi.fn(),
  })),
}));

vi.mock('@/convex/_generated/api', () => ({
  api: {
    users: {
      findByEmail: vi.fn(),
      findById: vi.fn(),
      findByClerkId: vi.fn(),
      getCurrentUser: vi.fn(),
      listUsers: vi.fn(),
      searchUsers: vi.fn(),
      updateProfile: vi.fn(),
      findByRole: vi.fn(),
      findByDepartment: vi.fn(),
    },
    properties: {
      getProperty: vi.fn(),
      listProperties: vi.fn(),
      createProperty: vi.fn(),
      updateProperty: vi.fn(),
      deleteProperty: vi.fn(),
      searchProperties: vi.fn(),
      getPropertiesByManager: vi.fn(),
      getPropertiesByRegion: vi.fn(),
      getPropertiesByType: vi.fn(),
      getPropertiesByStatus: vi.fn(),
      createPropertyArea: vi.fn(),
      getPropertyAreas: vi.fn(),
      updatePropertyArea: vi.fn(),
      deletePropertyArea: vi.fn(),
    },
  },
}));

// Mock fetch
global.fetch = vi.fn();

// Mock console methods for cleaner test output
global.console = {
  ...console,
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
};

// Mock performance
Object.defineProperty(global, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
    clearMarks: vi.fn(),
    clearMeasures: vi.fn(),
  },
  writable: true,
});

// Mock crypto
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: vi.fn(() => 'mock-uuid-12345678-1234-1234-1234-123456789abc'),
    subtle: {
      digest: vi.fn(),
    },
    getRandomValues: vi.fn(),
  },
  writable: true,
});

// Mock localStorage and sessionStorage
const mockStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  key: vi.fn(),
  length: 0,
};

Object.defineProperty(global, 'localStorage', {
  value: mockStorage,
  writable: true,
});

Object.defineProperty(global, 'sessionStorage', {
  value: mockStorage,
  writable: true,
});

// Mock ResizeObserver
Object.defineProperty(global, 'ResizeObserver', {
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  })),
  writable: true,
});

// Mock IntersectionObserver
Object.defineProperty(global, 'IntersectionObserver', {
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
    takeRecords: vi.fn(),
  })),
  writable: true,
});

// Mock matchMedia
Object.defineProperty(global, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(global, 'scrollTo', {
  value: vi.fn(),
  writable: true,
});

// Mock alert, confirm, prompt
Object.defineProperty(global, 'alert', { value: vi.fn(), writable: true });
Object.defineProperty(global, 'confirm', { value: vi.fn(), writable: true });
Object.defineProperty(global, 'prompt', { value: vi.fn(), writable: true });

// Clean up after each test
afterEach(() => {
  vi.clearAllMocks();
});

// Global test utilities
const testUtils = {
  createMockUser: (overrides = {}) => ({
    id: 'mock-user-id',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'User',
    department: 'General',
    ...overrides,
  }),
  
  createMockProperty: (overrides = {}) => ({
    id: 'mock-property-id',
    name: 'Test Property',
    address: '123 Test St',
    suburb: 'Testville',
    state: 'NSW',
    postcode: '2000',
    region: 'Sydney',
    type: 'Office',
    status: 'Active',
    ...overrides,
  }),
  
  waitFor: async (callback: () => void, timeout = 5000) => {
    const start = Date.now();
    while (Date.now() - start < timeout) {
      try {
        callback();
        return;
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }
    throw new Error('Timeout waiting for condition');
  },
};

// Make test utilities available globally
Object.defineProperty(global, 'testUtils', {
  value: testUtils,
  writable: true,
});