import { userRepository } from '@/app/repositories';
import ClientComponent from './client';

export default async function UsersPage() {
  // Get all users
  const users = await userRepository.getAll();

  // Note: Users from Convex don't have password field
  const usersWithoutPasswords = users;

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">Users Example</h1>

      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-gray-50 p-6 rounded-lg shadow">
          <h2 className="text-2xl font-bold mb-4">Server Component</h2>
          <p className="text-gray-600 mb-4">This data is fetched directly from the database using the repository pattern.</p>

          <div className="grid gap-4">
            {usersWithoutPasswords.map(user => (
              <div key={user._id} className="bg-white p-4 rounded shadow">
                <h2 className="text-xl font-semibold">{user.name}</h2>
                <p className="text-gray-600">{user.email}</p>
                <div className="mt-2">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm mr-2">
                    {user.role}
                  </span>
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                    {user.department}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-gray-50 p-6 rounded-lg shadow">
          <ClientComponent />
        </div>
      </div>
    </div>
  );
}
