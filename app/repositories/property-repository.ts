import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Define types based on Convex schema
export interface Property {
  _id: Id<"properties">;
  name: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  region: string;
  type: string;
  status: string;
  managerId?: Id<"users">;
  contractValue?: number;
  organizationId: Id<"organizations">;
  _creationTime: number;
}

export interface NewProperty {
  name: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  type: string;
  tier: number;
  region: string;
  sizeSqm?: number;
  category: string;
  managerId?: Id<"users">;
  metadata?: any;
}

export interface PropertyArea {
  _id: Id<"propertyAreas">;
  name: string;
  type: string;
  size?: number;
  cleaningFrequency?: string;
  lastCleaned?: number;
  propertyId: Id<"properties">;
  organizationId: Id<"organizations">;
  _creationTime: number;
}

export interface NewPropertyArea {
  name: string;
  type: string;
  size?: number;
  cleaningFrequency?: string;
  propertyId: Id<"properties">;
}

/**
 * Repository for property-related database operations
 * Wraps Convex queries and mutations with repository pattern
 */
export class PropertyRepository {
  /**
   * Find a property by ID
   */
  async findById(id: Id<"properties">): Promise<Property | null> {
    try {
      const result = await convex.query(api.properties.getProperty, { propertyId: id });
      return result;
    } catch (error) {
      console.error('Error finding property by ID:', error);
      throw error;
    }
  }

  /**
   * Get all properties in current organization
   */
  async getAll(options?: { limit?: number }): Promise<Property[]> {
    try {
      const result = await convex.query(api.properties.listProperties, {});
      return result;
    } catch (error) {
      console.error('Error getting all properties:', error);
      throw error;
    }
  }

  /**
   * Create a new property
   */
  async create(propertyData: NewProperty): Promise<Id<"properties">> {
    try {
      const result = await convex.mutation(api.properties.createProperty, propertyData);
      return result;
    } catch (error) {
      console.error('Error creating property:', error);
      throw error;
    }
  }

  /**
   * Update a property
   */
  async update(id: Id<"properties">, propertyData: Partial<NewProperty>): Promise<Id<"properties">> {
    try {
      const result = await convex.mutation(api.properties.updateProperty, {
        propertyId: id,
        ...propertyData
      });
      return result;
    } catch (error) {
      console.error('Error updating property:', error);
      throw error;
    }
  }

  /**
   * Delete a property
   */
  async delete(id: Id<"properties">): Promise<Id<"properties">> {
    try {
      const result = await convex.mutation(api.properties.deleteProperty, { propertyId: id });
      return result;
    } catch (error) {
      console.error('Error deleting property:', error);
      throw error;
    }
  }

  /**
   * Search properties by name, address, or suburb
   */
  async search(query: string, options?: { limit?: number }): Promise<Property[]> {
    try {
      const queryOptions: { query: string; limit?: number } = { query };
      if (options?.limit !== undefined) {
        queryOptions.limit = options.limit;
      }
      const result = await convex.query(api.properties.searchProperties, queryOptions);
      return result;
    } catch (error) {
      console.error('Error searching properties:', error);
      throw error;
    }
  }

  /**
   * Get properties by manager
   */
  async getByManager(managerId: Id<"users">, options?: { limit?: number }): Promise<Property[]> {
    try {
      const queryOptions: { managerId: Id<"users">; limit?: number } = { managerId };
      if (options?.limit !== undefined) {
        queryOptions.limit = options.limit;
      }
      const result = await convex.query(api.properties.getPropertiesByManager, queryOptions);
      return result;
    } catch (error) {
      console.error('Error getting properties by manager:', error);
      throw error;
    }
  }

  /**
   * Get properties by region
   */
  async getByRegion(region: string, options?: { limit?: number }): Promise<Property[]> {
    try {
      const queryOptions: { region: string; limit?: number } = { region };
      if (options?.limit !== undefined) {
        queryOptions.limit = options.limit;
      }
      const result = await convex.query(api.properties.getPropertiesByRegion, queryOptions);
      return result;
    } catch (error) {
      console.error('Error getting properties by region:', error);
      throw error;
    }
  }

  /**
   * Get properties by type
   */
  async getByType(type: string, options?: { limit?: number }): Promise<Property[]> {
    try {
      const queryOptions: { type: string; limit?: number } = { type };
      if (options?.limit !== undefined) {
        queryOptions.limit = options.limit;
      }
      const result = await convex.query(api.properties.getPropertiesByType, queryOptions);
      return result;
    } catch (error) {
      console.error('Error getting properties by type:', error);
      throw error;
    }
  }

  /**
   * Get properties by status
   */
  async getByStatus(status: string, options?: { limit?: number }): Promise<Property[]> {
    try {
      const queryOptions: { status: string; limit?: number } = { status };
      if (options?.limit !== undefined) {
        queryOptions.limit = options.limit;
      }
      const result = await convex.query(api.properties.getPropertiesByStatus, queryOptions);
      return result;
    } catch (error) {
      console.error('Error getting properties by status:', error);
      throw error;
    }
  }

  /**
   * Add an area to a property
   */
  async addArea(area: NewPropertyArea): Promise<Id<"propertyAreas">> {
    try {
      const result = await convex.mutation(api.properties.createPropertyArea, area);
      return result;
    } catch (error) {
      console.error('Error adding area to property:', error);
      throw error;
    }
  }

  /**
   * Get areas for a property
   */
  async getAreas(propertyId: Id<"properties">): Promise<PropertyArea[]> {
    try {
      const result = await convex.query(api.properties.getPropertyAreas, { propertyId });
      return result;
    } catch (error) {
      console.error('Error getting areas for property:', error);
      throw error;
    }
  }

  /**
   * Update a property area
   */
  async updateArea(id: Id<"propertyAreas">, areaData: Partial<Omit<NewPropertyArea, 'propertyId'>>): Promise<Id<"propertyAreas">> {
    try {
      const result = await convex.mutation(api.properties.updatePropertyArea, {
        areaId: id,
        ...areaData
      });
      return result;
    } catch (error) {
      console.error('Error updating property area:', error);
      throw error;
    }
  }

  /**
   * Delete a property area
   */
  async deleteArea(id: Id<"propertyAreas">): Promise<Id<"propertyAreas">> {
    try {
      const result = await convex.mutation(api.properties.deletePropertyArea, { areaId: id });
      return result;
    } catch (error) {
      console.error('Error deleting property area:', error);
      throw error;
    }
  }

  /**
   * Count total properties
   */
  async count(): Promise<number> {
    try {
      const properties = await this.getAll();
      return properties.length;
    } catch (error) {
      console.error('Error counting properties:', error);
      throw error;
    }
  }

  /**
   * Get property statistics by region
   */
  async getStatsByRegion(): Promise<{ region: string; count: number }[]> {
    try {
      const properties = await this.getAll();
      const regionCounts = properties.reduce((acc, property) => {
        acc[property.region] = (acc[property.region] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      return Object.entries(regionCounts).map(([region, count]) => ({
        region,
        count
      }));
    } catch (error) {
      console.error('Error getting property statistics by region:', error);
      throw error;
    }
  }

  /**
   * Get property statistics by type
   */
  async getStatsByType(): Promise<{ type: string; count: number }[]> {
    try {
      const properties = await this.getAll();
      const typeCounts = properties.reduce((acc, property) => {
        acc[property.type] = (acc[property.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      return Object.entries(typeCounts).map(([type, count]) => ({
        type,
        count
      }));
    } catch (error) {
      console.error('Error getting property statistics by type:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const propertyRepository = new PropertyRepository();
