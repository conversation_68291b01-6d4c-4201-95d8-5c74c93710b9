"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Mic, Camera, Monitor, Send, Settings, X } from "lucide-react"
import { useTheme } from "next-themes"
import { streamText } from "ai"
import { openai } from "@ai-sdk/openai"

interface Message {
  id: string
  content: string
  role: "user" | "assistant"
  isStreaming?: boolean
}

export function MobileChat() {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const [isCameraActive, setIsCameraActive] = useState(false)
  const [isScreenActive, setIsScreenActive] = useState(false)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [api<PERSON><PERSON>, setApi<PERSON><PERSON>] = useState("")
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { theme } = useTheme()
  const isDarkTheme = theme === "dark"

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const uploadFile = async (blob: Blob, filename: string): Promise<string> => {
    const formData = new FormData()
    formData.append("file", blob, filename)

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 seconds timeout

    try {
      const response = await fetch(`/api/upload?filename=${filename}`, {
        method: "POST",
        body: blob,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to upload file")
      }

      const data = await response.json()
      return data.url
    } catch (error) {
      if ((error as any).name === "AbortError") {
        throw new Error("Upload timed out. Please try again.")
      }
      throw error
    }
  }

  const handleSendMessage = async () => {
    if (!input.trim() || !isConnected) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: "user",
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")

    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      content: "",
      role: "assistant",
      isStreaming: true,
    }

    setMessages((prev) => [...prev, assistantMessage])

    try {
      const result = await streamText({
        model: openai("gpt-4o"),
        prompt: input,
        // Note: apiKey is already configured in openai() constructor
        onChunk: ({ chunk }) => {
          if (chunk.type === "text-delta") {
            setMessages((prev) =>
              prev.map((msg) => (msg.id === assistantMessage.id ? { ...msg, content: msg.content + chunk.textDelta } : msg)),
            )
          }
        },
      })

      const finalText = await result.text
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === assistantMessage.id ? { ...msg, content: finalText, isStreaming: false } : msg,
        ),
      )
    } catch (error) {
      console.error("Error generating response:", error)
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === assistantMessage.id
            ? { ...msg, content: "Sorry, I encountered an error. Please try again.", isStreaming: false }
            : msg,
        ),
      )
    }
  }

  const toggleMicrophone = async () => {
    if (!isConnected) return

    setIsRecording(!isRecording)
    if (!isRecording) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        const mediaRecorder = new MediaRecorder(stream)
        const audioChunks: Blob[] = []

        mediaRecorder.addEventListener("dataavailable", (event) => {
          audioChunks.push(event.data)
        })

        mediaRecorder.addEventListener("stop", async () => {
          const audioBlob = new Blob(audioChunks, { type: "audio/webm" })
          try {
            // Removed unused 'url' variable
            await uploadFile(audioBlob, "audio.webm")

            // Use fetch to call the Whisper API directly
            const response = await fetch("https://api.openai.com/v1/audio/transcriptions", {
              method: "POST",
              headers: {
                Authorization: `Bearer ${apiKey}`,
              },
              body: (() => {
                const formData = new FormData()
                formData.append("file", audioBlob, "audio.webm")
                formData.append("model", "whisper-1")
                return formData
              })(),
            })

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }

            const transcriptionResult = await response.json()
            setInput(transcriptionResult.text)
          } catch (error) {
            console.error("Error processing audio:", error)
            setInput(`Error: ${(error as any).message || 'Unknown error'}. Please try again.`)
          }
        })

        mediaRecorder.start()
        setTimeout(() => mediaRecorder.stop(), 5000) // Record for 5 seconds
      } catch (error) {
        console.error("Error recording audio:", error)
        setIsRecording(false)
      }
    } else {
      setIsRecording(false)
    }
  }

  const toggleCamera = async () => {
    if (!isConnected) return

    setIsCameraActive(!isCameraActive)
    if (!isCameraActive) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true })
        const videoElement = document.createElement("video")
        videoElement.srcObject = stream
        await videoElement.play()

        setTimeout(() => {
          const canvas = document.createElement("canvas")
          canvas.width = videoElement.videoWidth
          canvas.height = videoElement.videoHeight
          canvas.getContext("2d")?.drawImage(videoElement, 0, 0)
          canvas.toBlob(async (blob) => {
            if (blob) {
              try {
                const url = await uploadFile(blob, "image.jpg")
                setInput(`Analyze this image: ${url}`)
              } catch (error) {
                console.error("Error uploading image:", error)
                setInput(`Error: ${(error as any).message || 'Unknown error'}. Please try again.`)
              }
            }
          }, "image/jpeg")

          stream.getTracks().forEach((track) => track.stop())
        }, 1000) // Capture image after 1 second
      } catch (error) {
        console.error("Error accessing camera:", error)
        setIsCameraActive(false)
      }
    }
  }

  const toggleScreen = async () => {
    if (!isConnected) return

    setIsScreenActive(!isScreenActive)
    if (!isScreenActive) {
      try {
        const stream = await navigator.mediaDevices.getDisplayMedia({ video: true })
        const videoTrack = stream.getVideoTracks()[0]

        const imageCapture = new (window as any).ImageCapture(videoTrack)
        const bitmap = await imageCapture.grabFrame()

        const canvas = document.createElement("canvas")
        canvas.width = bitmap.width
        canvas.height = bitmap.height
        canvas.getContext("2d")?.drawImage(bitmap, 0, 0)

        canvas.toBlob(async (blob) => {
          if (blob) {
            try {
              const url = await uploadFile(blob, "screenshot.jpg")
              setInput(`Analyze this screenshot: ${url}`)
            } catch (error) {
              console.error("Error uploading screenshot:", error)
              setInput(`Error: ${(error as any).message || 'Unknown error'}. Please try again.`)
            }
          }
        }, "image/jpeg")

        stream.getTracks().forEach((track) => track.stop())
      } catch (error) {
        console.error("Error capturing screen:", error)
        setIsScreenActive(false)
      }
    }
  }

  const toggleSettings = () => {
    setIsSettingsOpen(!isSettingsOpen)
  }

  const toggleConnection = () => {
    if (apiKey) {
      setIsConnected(!isConnected)
    } else {
      alert("Please enter your API key in the settings before connecting.")
    }
  }

  const saveSettings = () => {
    localStorage.setItem("apiKey", apiKey)
    toggleSettings()
  }

  useEffect(() => {
    const savedApiKey = localStorage.getItem("apiKey")
    if (savedApiKey) {
      setApiKey(savedApiKey)
    }
  }, [])

  // ... (rest of the component code remains the same)

  return (
    <div className="relative flex flex-col h-screen bg-background">
      {/* Header */}
      <motion.div
        className="p-4 flex justify-between items-center border-b border-border/40 bg-gradient-to-b from-background/80 to-background/40 backdrop-blur-lg"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.button
          className={`px-3 py-1.5 rounded-lg ${isConnected ? "text-red-500 border border-red-500/50" : "text-green-500 border border-green-500/50"} bg-background/80 backdrop-blur-sm`}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={toggleConnection}
        >
          {isConnected ? "Disconnect" : "Connect"}
        </motion.button>

        <motion.h1
          className="text-lg font-semibold"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          Gemini Chat
        </motion.h1>

        <motion.button
          className="p-2 rounded-lg bg-background/80 backdrop-blur-sm border border-border/40"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={toggleSettings}
        >
          <Settings className="h-5 w-5" />
        </motion.button>
      </motion.div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div
                className={`relative max-w-[80%] rounded-xl p-3 ${
                  message.role === "user"
                    ? "bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-500/30 text-foreground"
                    : "bg-gradient-to-br from-background/80 to-background/40 border border-border/40 text-foreground"
                }`}
              >
                <motion.div
                  className="absolute -inset-px rounded-xl z-0 opacity-0 pointer-events-none"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: message.role === "user" ? 0.5 : 0.3 }}
                  style={{
                    background:
                      message.role === "user"
                        ? "radial-gradient(circle, rgba(59,130,246,0.3) 0%, rgba(37,99,235,0.1) 50%, rgba(29,78,216,0) 100%)"
                        : "radial-gradient(circle, rgba(249,115,22,0.3) 0%, rgba(234,88,12,0.1) 50%, rgba(194,65,12,0) 100%)",
                  }}
                />
                <p className="relative z-10">
                  {message.content}
                  {message.isStreaming && <span className="ml-1 inline-block w-2 h-4 bg-foreground animate-pulse" />}
                </p>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
        <div ref={messagesEndRef} />
      </div>

      {/* Preview Area */}
      <div className="relative">
        {isCameraActive && (
          <motion.div
            className="absolute bottom-20 left-4 w-32 h-24 bg-background/80 backdrop-blur-sm border border-border/40 rounded-lg overflow-hidden"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <Camera className="h-8 w-8 opacity-50" />
            </div>
          </motion.div>
        )}

        {isScreenActive && (
          <motion.div
            className="absolute bottom-20 left-40 w-32 h-24 bg-background/80 backdrop-blur-sm border border-border/40 rounded-lg overflow-hidden"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <Monitor className="h-8 w-8 opacity-50" />
            </div>
          </motion.div>
        )}
      </div>

      {/* Input Area */}
      <motion.div
        className="p-4 border-t border-border/40 bg-gradient-to-t from-background/80 to-background/40 backdrop-blur-lg"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
              placeholder="Type your message..."
              className="w-full px-4 py-2 rounded-xl bg-background/80 backdrop-blur-sm border border-border/40 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
              disabled={!isConnected}
            />
          </div>

          <motion.button
            className={`p-2 rounded-full ${isRecording ? "bg-red-500 text-white" : "bg-background/80 backdrop-blur-sm border border-border/40"}`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={toggleMicrophone}
            disabled={!isConnected}
          >
            <Mic className="h-5 w-5" />
          </motion.button>

          <motion.button
            className={`p-2 rounded-full ${isCameraActive ? "bg-green-500 text-white" : "bg-background/80 backdrop-blur-sm border border-border/40"}`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={toggleCamera}
            disabled={!isConnected}
          >
            <Camera className="h-5 w-5" />
          </motion.button>

          <motion.button
            className={`p-2 rounded-full ${isScreenActive ? "bg-blue-500 text-white" : "bg-background/80 backdrop-blur-sm border border-border/40"}`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={toggleScreen}
            disabled={!isConnected}
          >
            <Monitor className="h-5 w-5" />
          </motion.button>

          <motion.button
            className="p-2 rounded-full bg-blue-500 text-white"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleSendMessage}
            disabled={!isConnected}
          >
            <Send className="h-5 w-5" />
          </motion.button>
        </div>
      </motion.div>

      {/* Settings Modal */}
      <AnimatePresence>
        {isSettingsOpen && (
          <>
            <motion.div
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={toggleSettings}
            />

            <motion.div
              className="fixed inset-x-4 top-1/2 -translate-y-1/2 bg-background border border-border/40 rounded-xl p-6 z-50 max-h-[80vh] overflow-y-auto"
              initial={{ opacity: 0, scale: 0.9, y: "-40%" }}
              animate={{ opacity: 1, scale: 1, y: "-50%" }}
              exit={{ opacity: 0, scale: 0.9, y: "-40%" }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
            >
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Settings</h2>
                <motion.button whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }} onClick={toggleSettings}>
                  <X className="h-5 w-5" />
                </motion.button>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium">API Key</label>
                  <input
                    type="password"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="Enter your API key"
                    className="w-full px-3 py-2 rounded-lg bg-background/80 border border-border/40"
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium">Voice</label>
                  <select className="w-full px-3 py-2 rounded-lg bg-background/80 border border-border/40">
                    <option>Aoede</option>
                    <option>Puck</option>
                    <option>Charon</option>
                    <option>Kore</option>
                    <option>Fenrir</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium">Sample Rate</label>
                  <input type="range" min="8000" max="48000" step="1000" defaultValue="27000" className="w-full" />
                  <div className="text-xs text-right">27000 Hz</div>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium">System Instructions</label>
                  <textarea
                    rows={3}
                    placeholder="Enter system instructions"
                    defaultValue="You are a helpful assistant"
                    className="w-full px-3 py-2 rounded-lg bg-background/80 border border-border/40"
                  />
                </div>

                <motion.button
                  className="w-full py-2 rounded-lg bg-blue-500 text-white font-medium mt-4"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={saveSettings}
                >
                  Save Settings
                </motion.button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Audio Visualizer */}
      <div className="absolute bottom-0 left-0 right-0 h-32 pointer-events-none">
        <svg width="100%" height="100%" preserveAspectRatio="none">
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor={isDarkTheme ? "#4CAF50" : "#81C784"} />
              <stop offset="50%" stopColor={isDarkTheme ? "#81C784" : "#A5D6A7"} />
              <stop offset="100%" stopColor={isDarkTheme ? "#A5D6A7" : "#C8E6C9"} />
            </linearGradient>
          </defs>
          {isRecording && (
            <motion.path
              d="M0,50 Q25,30 50,50 Q75,70 100,50 Q125,30 150,50 Q175,70 200,50 Q225,30 250,50 Q275,70 300,50 Q325,30 350,50 Q375,70 400,50"
              fill="none"
              stroke="url(#gradient)"
              strokeWidth="3"
              strokeLinecap="round"
              initial={{ opacity: 0, pathLength: 0 }}
              animate={{
                opacity: 1,
                pathLength: 1,
                d: [
                  "M0,50 Q25,30 50,50 Q75,70 100,50 Q125,30 150,50 Q175,70 200,50 Q225,30 250,50 Q275,70 300,50 Q325,30 350,50 Q375,70 400,50",
                  "M0,50 Q25,40 50,50 Q75,60 100,50 Q125,40 150,50 Q175,60 200,50 Q225,40 250,50 Q275,60 300,50 Q325,40 350,50 Q375,60 400,50",
                  "M0,50 Q25,20 50,50 Q75,80 100,50 Q125,20 150,50 Q175,80 200,50 Q225,20 250,50 Q275,80 300,50 Q325,20 350,50 Q375,80 400,50",
                ],
              }}
              transition={{
                d: {
                  repeat: Number.POSITIVE_INFINITY,
                  repeatType: "reverse",
                  duration: 2,
                  ease: "easeInOut",
                },
              }}
            />
          )}
        </svg>
      </div>
    </div>
  )
}
