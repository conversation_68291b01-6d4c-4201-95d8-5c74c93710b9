"use client"

import { useState, useEffect, useCallback } from "react"
import { ArrowUp, ArrowDown, Clock, CheckCircle, AlertTriangle, Search, Filter } from "lucide-react"
import { motion } from "framer-motion"
import { Progress } from "@/components/ui/progress"

interface UserData {
  name: string
  role: string
  department: string
}

interface InspectionDashboardProps {
  onNavigate: (screen: string) => void
  userData: UserData
}

// Removed unused props: onNavigate, userData
export function InspectionDashboard({ }: InspectionDashboardProps) {
  const [activeTab, setActiveTab] = useState("quality")
  // Removed unused state: isRecording, setIsRecording, selectedFilter, setSelectedFilter
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // State for dashboard data
  const [topSites, setTopSites] = useState<Array<{id: string, name: string, score: number, isPositive: boolean, change: string}>>([])
  const [complianceData, setComplianceData] = useState<Array<{id: string, category: string, status: string, score: number, dueDate: string}>>([])
  const [inspectionsData, setInspectionsData] = useState<Array<{id: string, property: string, type: string, status: string, findings: number, date: string, inspector: string}>>([])
  const [qualityMetrics, setQualityMetrics] = useState<Array<{metric: string, score: number, target: number, status: string}>>([])
  const [criticalIssues, setCriticalIssues] = useState<Array<{id: string, location: string, issue: string, priority: string, status: string, dueDate: string, assignedTo: string}>>([])

  const fetchDashboardData = useCallback(async () => { // Wrap in useCallback
    setIsLoading(true)
    setError(null)

    try {
      // In a real app, these would be API calls
      // const response = await fetch(`/api/dashboard/${activeTab}`)
      // const data = await response.json()

      // Set empty arrays for now
      setTopSites([])
      setComplianceData([])
      setInspectionsData([])
      setQualityMetrics([])
      setQualityMetrics([])
      setCriticalIssues([])
  } catch (err: unknown) { // Type err as unknown
      console.error(`Error fetching ${activeTab} data:`, err)
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage || `Failed to load ${activeTab} data`)
    } finally {
      setIsLoading(false)
    }
  }, [activeTab])

  useEffect(() => {
    // Fetch dashboard data based on active tab
    fetchDashboardData()
  }, [activeTab, fetchDashboardData]) // Add fetchDashboardData as dependency since it's wrapped in useCallback

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
      case "passed":
        return "text-green-400"
      case "in progress":
      case "pending":
        return "text-amber-400"
      case "failed":
      case "overdue":
        return "text-red-400"
      default:
        return "text-gray-400"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "low":
        return "bg-blue-500/20 text-blue-400"
      case "medium":
        return "bg-amber-500/20 text-amber-400"
      case "high":
        return "bg-orange-500/20 text-orange-400"
      case "critical":
        return "bg-red-500/20 text-red-400"
      default:
        return "bg-gray-500/20 text-gray-400"
    }
  }

  const renderComplianceTab = () => (
    <div className="flex-1 flex flex-col w-full px-6 mt-4 overflow-y-auto pb-24">
      {isLoading ? (
        <div className="flex items-center justify-center p-12">
          <div className="w-8 h-8 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
        </div>
      ) : error ? (
        <div className="p-6 text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <button className="px-4 py-2 bg-[#3D4D61] text-white rounded-md text-sm" onClick={fetchDashboardData}>
            Try Again
          </button>
        </div>
      ) : (
        <>
          {/* Compliance Overview */}
          <div className="w-full bg-black/30 backdrop-blur-lg rounded-xl p-4 mb-4 border border-white/10 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-white text-lg font-medium">Compliance Overview</h2>
              <span className="text-[#ffffffb2] text-sm">Last 30 Days</span>
            </div>

            <div className="grid grid-cols-3 gap-3">
              <motion.div
                className="bg-black/40 rounded-xl p-3 border border-white/10 shadow-sm"
                whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
              >
                <p className="text-[#ffffffb2] text-sm mb-1">Overall</p>
                <p className="text-white text-2xl font-bold">-</p>
                <div className="flex items-center text-gray-400 text-sm mt-1">
                  <span>No data</span>
                </div>
              </motion.div>

              <motion.div
                className="bg-black/40 rounded-xl p-3 border border-white/10 shadow-sm"
                whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
              >
                <p className="text-[#ffffffb2] text-sm mb-1">Regulatory</p>
                <p className="text-white text-2xl font-bold">-</p>
                <div className="flex items-center text-gray-400 text-sm mt-1">
                  <span>No data</span>
                </div>
              </motion.div>

              <motion.div
                className="bg-black/40 rounded-xl p-3 border border-white/10 shadow-sm"
                whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
              >
                <p className="text-[#ffffffb2] text-sm mb-1">Internal</p>
                <p className="text-white text-2xl font-bold">-</p>
                <div className="flex items-center text-gray-400 text-sm mt-1">
                  <span>No data</span>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Compliance Categories */}
          <div className="w-full bg-black/30 backdrop-blur-lg rounded-xl p-4 mb-4 border border-white/10 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-white text-lg font-medium">Compliance Categories</h2>
              <motion.button className="text-[#A4D321] text-sm" whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                View All
              </motion.button>
            </div>

            {complianceData.length > 0 ? (
              <div className="space-y-4">
                {complianceData.map((item: any) => (
                  <motion.div
                    key={(item as any).id}
                    className="bg-black/40 rounded-lg p-3 border border-white/10"
                    whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-white text-sm font-medium">{(item as any).category}</h3>
                        <p className={`text-xs mt-1 ${getStatusColor((item as any).status)}`}>{(item as any).status}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-white text-sm font-bold">{(item as any).score}%</p>
                        {(item as any).dueDate !== "N/A" && <p className="text-[#ffffffb2] text-xs mt-1">Due: {(item as any).dueDate}</p>}
                      </div>
                    </div>
                    <div className="mt-2">
                      <Progress value={(item as any).score} className="h-1.5 bg-white/10">
                        <div
                          className={`h-full rounded-full ${
                            (item as any).score >= 95 ? "bg-green-500" : (item as any).score >= 85 ? "bg-amber-500" : "bg-red-500"
                          }`}
                        />
                      </Progress>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="p-6 text-center">
                <p className="text-[#ffffffb2]">No compliance data available</p>
              </div>
            )}
          </div>

          {/* Upcoming Deadlines */}
          <div className="w-full bg-black/30 backdrop-blur-lg rounded-xl p-4 mb-4 border border-white/10 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-white text-lg font-medium">Upcoming Deadlines</h2>
              <motion.button className="text-[#A4D321] text-sm" whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                Calendar
              </motion.button>
            </div>

            <div className="p-6 text-center">
              <p className="text-[#ffffffb2]">No upcoming deadlines</p>
            </div>
          </div>
        </>
      )}
    </div>
  )

  const renderInspectionsTab = () => (
    <div className="flex-1 flex flex-col w-full px-6 mt-4 overflow-y-auto pb-24">
      {/* Search and Filter */}
      <div className="flex items-center space-x-2 mb-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#ffffffb2]" />
          <input
            type="text"
            placeholder="Search inspections..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-9 pr-3 py-2 rounded-lg bg-black/40 backdrop-blur-sm border border-white/10 focus:outline-none focus:ring-1 focus:ring-[#A4D321]/50 text-white text-sm"
          />
        </div>
        <motion.button
          className="p-2 rounded-lg bg-black/40 backdrop-blur-sm border border-white/10 text-white"
          whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.1)" }}
        >
          <Filter className="h-4 w-4" />
        </motion.button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center p-12">
          <div className="w-8 h-8 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
        </div>
      ) : error ? (
        <div className="p-6 text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <button className="px-4 py-2 bg-[#3D4D61] text-white rounded-md text-sm" onClick={fetchDashboardData}>
            Try Again
          </button>
        </div>
      ) : (
        <>
          {/* Inspection Stats */}
          <div className="grid grid-cols-3 gap-3 mb-4">
            <motion.div
              className="bg-black/30 backdrop-blur-lg rounded-xl p-3 border border-white/10 shadow-sm"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center mb-1">
                  <CheckCircle className="h-4 w-4 text-green-400" />
                </div>
                <p className="text-white text-lg font-bold">-</p>
                <p className="text-[#ffffffb2] text-xs">Completed</p>
              </div>
            </motion.div>

            <motion.div
              className="bg-black/30 backdrop-blur-lg rounded-xl p-3 border border-white/10 shadow-sm"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 rounded-full bg-amber-500/20 flex items-center justify-center mb-1">
                  <Clock className="h-4 w-4 text-amber-400" />
                </div>
                <p className="text-white text-lg font-bold">-</p>
                <p className="text-[#ffffffb2] text-xs">Scheduled</p>
              </div>
            </motion.div>

            <motion.div
              className="bg-black/30 backdrop-blur-lg rounded-xl p-3 border border-white/10 shadow-sm"
              whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
            >
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center mb-1">
                  <AlertTriangle className="h-4 w-4 text-red-400" />
                </div>
                <p className="text-white text-lg font-bold">-</p>
                <p className="text-[#ffffffb2] text-xs">Overdue</p>
              </div>
            </motion.div>
          </div>

          {/* Recent Inspections */}
          <div className="w-full bg-black/30 backdrop-blur-lg rounded-xl p-4 mb-4 border border-white/10 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-white text-lg font-medium">Recent Inspections</h2>
              <motion.button className="text-[#A4D321] text-sm" whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                View All
              </motion.button>
            </div>

            {inspectionsData.length > 0 ? (
              <div className="space-y-3">
                {inspectionsData.map((inspection) => (
                  <motion.div
                    key={(inspection as any).id}
                    className="bg-black/40 rounded-lg p-3 border border-white/10"
                    whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-white text-sm font-medium">{(inspection as any).property}</h3>
                        <p className="text-[#ffffffb2] text-xs mt-0.5">{(inspection as any).type} Inspection</p>
                        <div className="flex items-center mt-1">
                          <p className={`text-xs ${getStatusColor((inspection as any).status)}`}>{(inspection as any).status}</p>
                          {(inspection as any).findings > 0 && (
                            <p className="text-amber-400 text-xs ml-2">
                              {(inspection as any).findings} {(inspection as any).findings === 1 ? "finding" : "findings"}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-[#ffffffb2] text-xs">{(inspection as any).date}</p>
                        <p className="text-white text-xs mt-0.5">{(inspection as any).inspector}</p>
                        <motion.button
                          className="mt-1 text-[#A4D321] text-xs"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          Details
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="p-6 text-center">
                <p className="text-[#ffffffb2]">No recent inspections</p>
              </div>
            )}
          </div>

          {/* Upcoming Inspections */}
          <div className="w-full bg-black/30 backdrop-blur-lg rounded-xl p-4 mb-4 border border-white/10 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-white text-lg font-medium">Upcoming Inspections</h2>
              <motion.button className="text-[#A4D321] text-sm" whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                Schedule
              </motion.button>
            </div>

            <div className="p-6 text-center">
              <p className="text-[#ffffffb2]">No upcoming inspections</p>
            </div>
          </div>
        </>
      )}
    </div>
  )

  const renderQualityTab = () => (
    <div className="flex-1 flex flex-col w-full px-6 mt-4 overflow-y-auto pb-24">
      {isLoading ? (
        <div className="flex items-center justify-center p-12">
          <div className="w-8 h-8 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
        </div>
      ) : error ? (
        <div className="p-6 text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <button className="px-4 py-2 bg-[#3D4D61] text-white rounded-md text-sm" onClick={fetchDashboardData}>
            Try Again
          </button>
        </div>
      ) : (
        <>
          {/* Performance Overview */}
          <div className="w-full bg-black/30 backdrop-blur-lg rounded-xl p-4 mb-4 border border-white/10 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-white text-lg font-medium">Performance Overview</h2>
              <span className="text-[#ffffffb2] text-sm">Last 30 Days</span>
            </div>

            <div className="grid grid-cols-3 gap-3">
              <motion.div
                className="bg-black/40 rounded-xl p-3 border border-white/10 shadow-sm"
                whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
              >
                <p className="text-[#ffffffb2] text-sm mb-1">Overall</p>
                <p className="text-white text-2xl font-bold">-</p>
                <div className="flex items-center text-gray-400 text-sm mt-1">
                  <span>No data</span>
                </div>
              </motion.div>

              <motion.div
                className="bg-black/40 rounded-xl p-3 border border-white/10 shadow-sm"
                whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
              >
                <p className="text-[#ffffffb2] text-sm mb-1">Critical Areas</p>
                <p className="text-white text-2xl font-bold">-</p>
                <div className="flex items-center text-gray-400 text-sm mt-1">
                  <span>No data</span>
                </div>
              </motion.div>

              <motion.div
                className="bg-black/40 rounded-xl p-3 border border-white/10 shadow-sm"
                whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
              >
                <p className="text-[#ffffffb2] text-sm mb-1">Non-Critical</p>
                <p className="text-white text-2xl font-bold">-</p>
                <div className="flex items-center text-gray-400 text-sm mt-1">
                  <span>No data</span>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Quality Metrics */}
          <div className="w-full bg-black/30 backdrop-blur-lg rounded-xl p-4 mb-4 border border-white/10 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-white text-lg font-medium">Quality Metrics</h2>
              <motion.button className="text-[#A4D321] text-sm" whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                View All
              </motion.button>
            </div>

            {qualityMetrics.length > 0 ? (
              <div className="space-y-4">
                {qualityMetrics.map((metric, index) => (
                  <motion.div
                    key={index}
                    className="bg-black/40 rounded-lg p-3 border border-white/10"
                    whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
                  >
                    <div className="flex justify-between items-start mb-1">
                      <h3 className="text-white text-sm font-medium">{(metric as {metric: string}).metric}</h3>
                      <div className="flex items-center">
                        <p className="text-white text-sm font-bold">{(metric as any).score}%</p>
                        <span className="text-[#ffffffb2] text-xs ml-2">Target: {(metric as any).target}%</span>
                      </div>
                    </div>
                    <div className="mt-2">
                      <Progress value={metric.score} className="h-1.5 bg-white/10">
                        <div
                          className={`h-full rounded-full ${
                            metric.status === "Above Target" ? "bg-green-500" : "bg-amber-500"
                          }`}
                        />
                      </Progress>
                    </div>
                    <p
                      className={`text-xs mt-1 ${metric.status === "Above Target" ? "text-green-400" : "text-amber-400"}`}
                    >
                      {metric.status}
                    </p>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="p-6 text-center">
                <p className="text-[#ffffffb2]">No quality metrics available</p>
              </div>
            )}
          </div>

          {/* Top Performing Sites */}
          <div className="w-full bg-black/30 backdrop-blur-lg rounded-xl p-4 mb-4 border border-white/10 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-white text-lg font-medium">Top Performing Sites</h2>
              <motion.button className="text-[#A4D321] text-sm" whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                View All
              </motion.button>
            </div>

            {topSites.length > 0 ? (
              <div className="space-y-4">
                {topSites.map((site) => (
                  <motion.div
                    key={site.id}
                    className="flex items-center justify-between border-b border-white/10 pb-4 last:border-0 last:pb-0"
                    whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.02)" }}
                  >
                    <div className="flex items-center">
                      <span className="text-[#ffffffb2] text-xl mr-4">{site.id}</span>
                      <span className="text-white text-base">{site.name}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-white text-xl font-bold mr-3">{site.score}</span>
                      <div
                        className={`flex items-center ${site.isPositive ? "text-green-400" : "text-red-400"} text-sm`}
                      >
                        {site.isPositive ? (
                          <ArrowUp className="w-3.5 h-3.5 mr-0.5" />
                        ) : (
                          <ArrowDown className="w-3.5 h-3.5 mr-0.5" />
                        )}
                        <span>{site.change.replace("+", "").replace("-", "")}</span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="p-6 text-center">
                <p className="text-[#ffffffb2]">No site performance data available</p>
              </div>
            )}
          </div>

          {/* Critical Issues */}
          <div className="w-full bg-black/30 backdrop-blur-lg rounded-xl p-4 mb-4 border border-white/10 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-white text-lg font-medium">Critical Issues</h2>
              <motion.button className="text-[#A4D321] text-sm" whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                View All
              </motion.button>
            </div>

            {criticalIssues.length > 0 ? (
              <div className="space-y-3">
                {criticalIssues.map((issue) => (
                  <motion.div
                    key={issue.id}
                    className="bg-black/40 rounded-lg p-3 border border-white/10"
                    whileHover={{ backgroundColor: "rgba(255, 255, 255, 0.05)" }}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-white text-sm font-medium">{issue.location}</h3>
                        <p className="text-[#ffffffb2] text-xs mt-1">{issue.issue}</p>
                        <div className="flex items-center mt-2">
                          <span className={`text-xs px-2 py-0.5 rounded-full ${getPriorityColor(issue.priority)}`}>
                            {issue.priority}
                          </span>
                          <span className="text-[#ffffffb2] text-xs ml-2">{issue.status}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-[#ffffffb2] text-xs">Due: {issue.dueDate}</p>
                        <p className="text-white text-xs mt-1">{issue.assignedTo}</p>
                        <motion.button
                          className="mt-2 text-[#A4D321] text-xs"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          View Details
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="p-6 text-center">
                <p className="text-[#ffffffb2]">No critical issues found</p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )

  return (
    <div className="flex flex-col w-full h-full min-h-screen relative">
      {/* Tab Navigation */}
      <div className="flex w-full px-6 mt-4">
        <div className="w-full bg-black/40 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm">
          <div className="flex w-full">
            <motion.button
              className={`flex-1 py-3 text-center text-sm ${
                activeTab === "compliance" ? "bg-black/50 text-white" : "text-[#ffffffb2]"
              }`}
              onClick={() => setActiveTab("compliance")}
              whileHover={activeTab !== "compliance" ? { backgroundColor: "rgba(255, 255, 255, 0.05)" } : {}}
            >
              Compliance
            </motion.button>
            <motion.button
              className={`flex-1 py-3 text-center text-sm ${
                activeTab === "inspections" ? "bg-black/50 text-white" : "text-[#ffffffb2]"
              }`}
              onClick={() => setActiveTab("inspections")}
              whileHover={activeTab !== "inspections" ? { backgroundColor: "rgba(255, 255, 255, 0.05)" } : {}}
            >
              Inspections
            </motion.button>
            <motion.button
              className={`flex-1 py-3 text-center text-sm ${
                activeTab === "quality" ? "bg-black/50 text-white font-medium" : "text-[#ffffffb2]"
              }`}
              onClick={() => setActiveTab("quality")}
              whileHover={activeTab !== "quality" ? { backgroundColor: "rgba(255, 255, 255, 0.05)" } : {}}
            >
              Quality
            </motion.button>
          </div>
        </div>
      </div>

      {/* Dashboard Content */}
      {activeTab === "compliance" && renderComplianceTab()}
      {activeTab === "inspections" && renderInspectionsTab()}
      {activeTab === "quality" && renderQualityTab()}
    </div>
  )
}
