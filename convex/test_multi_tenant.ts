/**
 * Multi-tenant testing functions
 * These are internal functions for testing organization isolation
 */
import { v } from "convex/values";
import { internalMutation, internalQuery } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Test creating organizations
export const createTestOrganizations = internalMutation({
  args: {},
  handler: async (ctx) => {
    // Create test organizations
    const org1Id = await ctx.db.insert("organizations", {
      clerkOrgId: "test_org_1",
      name: "ARA Property Services - Test Org 1",
      slug: "test-org-1",
      privateMetadata: { test: true },
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    const org2Id = await ctx.db.insert("organizations", {
      clerkOrgId: "test_org_2", 
      name: "ARA Property Services - Test Org 2",
      slug: "test-org-2", 
      privateMetadata: { test: true },
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    return { org1Id, org2Id };
  }
});

// Test creating users in different organizations
export const createTestUsers = internalMutation({
  args: {
    org1Id: v.id("organizations"),
    org2Id: v.id("organizations")
  },
  handler: async (ctx, args) => {
    // Create users in org1
    const user1Id = await ctx.db.insert("users", {
      clerkUserId: "test_user_1",
      email: "<EMAIL>",
      name: "User 1 Org 1",
      role: "admin",
      department: "Operations",
      organizationId: args.org1Id
    });

    const user2Id = await ctx.db.insert("users", {
      clerkUserId: "test_user_2", 
      email: "<EMAIL>",
      name: "User 2 Org 1",
      role: "staff",
      department: "Cleaning",
      organizationId: args.org1Id
    });

    // Create users in org2
    const user3Id = await ctx.db.insert("users", {
      clerkUserId: "test_user_3",
      email: "<EMAIL>",
      name: "User 1 Org 2", 
      role: "admin",
      department: "Operations",
      organizationId: args.org2Id
    });

    const user4Id = await ctx.db.insert("users", {
      clerkUserId: "test_user_4",
      email: "<EMAIL>",
      name: "User 2 Org 2",
      role: "staff", 
      department: "Cleaning",
      organizationId: args.org2Id
    });

    return { user1Id, user2Id, user3Id, user4Id };
  }
});

// Test creating properties in different organizations
export const createTestProperties = internalMutation({
  args: {
    org1Id: v.id("organizations"),
    org2Id: v.id("organizations"),
    user1Id: v.id("users"),
    user3Id: v.id("users")
  },
  handler: async (ctx, args) => {
    // Properties for org1
    const prop1Id = await ctx.db.insert("properties", {
      name: "Org1 Property 1",
      address: "123 Org1 Street",
      suburb: "Org1 Suburb",
      state: "NSW",
      postcode: "2000",
      region: "Sydney",
      type: "Office",
      tier: 1,
      category: "Commercial",
      status: "active",
      managerId: args.user1Id,
      organizationId: args.org1Id
    });

    const prop2Id = await ctx.db.insert("properties", {
      name: "Org1 Property 2", 
      address: "456 Org1 Avenue",
      suburb: "Org1 City",
      state: "NSW",
      postcode: "2001",
      region: "Sydney",
      type: "Retail",
      tier: 2,
      category: "Retail",
      status: "active",
      managerId: args.user1Id,
      organizationId: args.org1Id
    });

    // Properties for org2
    const prop3Id = await ctx.db.insert("properties", {
      name: "Org2 Property 1",
      address: "789 Org2 Street",
      suburb: "Org2 Suburb", 
      state: "VIC",
      postcode: "3000",
      region: "Melbourne",
      type: "Office",
      tier: 1,
      category: "Commercial",
      status: "active",
      managerId: args.user3Id,
      organizationId: args.org2Id
    });

    const prop4Id = await ctx.db.insert("properties", {
      name: "Org2 Property 2",
      address: "101 Org2 Road",
      suburb: "Org2 Town",
      state: "VIC", 
      postcode: "3001",
      region: "Melbourne",
      type: "Warehouse",
      tier: 3,
      category: "Industrial",
      status: "active",
      managerId: args.user3Id,
      organizationId: args.org2Id
    });

    return { prop1Id, prop2Id, prop3Id, prop4Id };
  }
});

// Test data isolation - verify org1 users can only see org1 data
export const testOrg1DataIsolation = internalQuery({
  args: {
    org1Id: v.id("organizations"),
    user1Id: v.id("users")
  },
  handler: async (ctx, args) => {
    // Get all users in org1
    const org1Users = await ctx.db
      .query("users")
      .withIndex("by_org", (q) => q.eq("organizationId", args.org1Id))
      .collect();

    // Get all properties in org1
    const org1Properties = await ctx.db
      .query("properties") 
      .withIndex("by_org", (q) => q.eq("organizationId", args.org1Id))
      .collect();

    return {
      org1UsersCount: org1Users.length,
      org1PropertiesCount: org1Properties.length,
      org1Users: org1Users.map(u => ({ name: u.name, email: u.email })),
      org1Properties: org1Properties.map(p => ({ name: p.name, address: p.address }))
    };
  }
});

// Test data isolation - verify org2 users can only see org2 data  
export const testOrg2DataIsolation = internalQuery({
  args: {
    org2Id: v.id("organizations"),
    user3Id: v.id("users")
  },
  handler: async (ctx, args) => {
    // Get all users in org2
    const org2Users = await ctx.db
      .query("users")
      .withIndex("by_org", (q) => q.eq("organizationId", args.org2Id))
      .collect();

    // Get all properties in org2
    const org2Properties = await ctx.db
      .query("properties")
      .withIndex("by_org", (q) => q.eq("organizationId", args.org2Id))
      .collect();

    return {
      org2UsersCount: org2Users.length,
      org2PropertiesCount: org2Properties.length,
      org2Users: org2Users.map(u => ({ name: u.name, email: u.email })),
      org2Properties: org2Properties.map(p => ({ name: p.name, address: p.address }))
    };
  }
});

// Test cross-organization access prevention
export const testCrossOrgAccessPrevention = internalQuery({
  args: {
    org1Id: v.id("organizations"),
    org2Id: v.id("organizations")
  },
  handler: async (ctx, args) => {
    // Try to get org2 data while filtering by org1
    const org2UsersWithOrg1Filter = await ctx.db
      .query("users")
      .withIndex("by_org", (q) => q.eq("organizationId", args.org1Id))
      .filter((q) => q.eq(q.field("organizationId"), args.org2Id))
      .collect();

    const org2PropertiesWithOrg1Filter = await ctx.db
      .query("properties")
      .withIndex("by_org", (q) => q.eq("organizationId", args.org1Id))
      .filter((q) => q.eq(q.field("organizationId"), args.org2Id))
      .collect();

    // These should be empty arrays as users can't access other org data
    return {
      crossOrgUsersFound: org2UsersWithOrg1Filter.length,
      crossOrgPropertiesFound: org2PropertiesWithOrg1Filter.length,
      shouldBeZero: org2UsersWithOrg1Filter.length === 0 && org2PropertiesWithOrg1Filter.length === 0
    };
  }
});

// Clean up test data
export const cleanupTestData = internalMutation({
  args: {},
  handler: async (ctx) => {
    // Delete all test organizations and their data
    const testOrgs = await ctx.db
      .query("organizations")
      .filter((q) => q.neq(q.field("privateMetadata"), undefined))
      .collect();

    let deletedCount = 0;

    for (const org of testOrgs) {
      if (org.privateMetadata?.test) {
        // Delete all users in this org
        const users = await ctx.db
          .query("users")
          .withIndex("by_org", (q) => q.eq("organizationId", org._id))
          .collect();
        
        for (const user of users) {
          await ctx.db.delete(user._id);
          deletedCount++;
        }

        // Delete all properties in this org
        const properties = await ctx.db
          .query("properties")
          .withIndex("by_org", (q) => q.eq("organizationId", org._id))
          .collect();
        
        for (const property of properties) {
          await ctx.db.delete(property._id);
          deletedCount++;
        }

        // Delete the organization
        await ctx.db.delete(org._id);
        deletedCount++;
      }
    }

    return { deletedCount };
  }
});