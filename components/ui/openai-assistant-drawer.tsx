"use client"

import { useState, useEffect, useCallback } from "react"
import { motion } from "framer-motion"
import { LogoButton } from "@/components/ui/logo-button"
import { Drawer } from "vaul"

interface OpenAIAssistantDrawerProps {
  isOpen: boolean
  onClose: () => void
  isRecording: boolean
  onVoiceToggle: () => void
  transcription: string
}

export function OpenAIAssistantDrawer({
  isOpen,
  onClose,
  isRecording,
  onVoiceToggle,
  transcription,
}: OpenAIAssistantDrawerProps) {
  const [callInfo, setCallInfo] = useState<Record<string, unknown> | null>(null); // Use Record or specific interface
  const [isConnecting, setIsConnecting] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const initializeOpenAI = useCallback(async () => {
    try {
      setIsConnecting(true)
      setError(null)

      // Get credentials from the API
      const response = await fetch('/api/openai/credentials')
      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      setCallInfo(data)

      // Connect to the OpenAI agent
      const connectResponse = await fetch('/api/openai/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          callType: data.callType,
          callId: data.callId,
        }),
      })

      const connectData = await connectResponse.json()

      if (connectData.error) {
        throw new Error(connectData.error)
      }

      setIsConnected(true)
      setIsConnecting(false)
    } catch (err: unknown) {
      console.error('Error initializing OpenAI:', err)
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage || 'Failed to initialize OpenAI')
      setIsConnecting(false)
    }
  }, [])

  // Initialize the OpenAI connection when the drawer opens
  useEffect(() => {
    if (isOpen && !callInfo && !isConnecting && !isConnected) {
      initializeOpenAI()
    }
  }, [isOpen, callInfo, isConnecting, isConnected, initializeOpenAI])

  // Clean up when the drawer closes
  useEffect(() => {
    if (!isOpen) {
      // Reset state when drawer closes
      setCallInfo(null)
      setIsConnected(false)
    }
  }, [isOpen])
  
  return (
    <Drawer.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-40" />
        <Drawer.Content className="bg-black/90 backdrop-blur-md flex flex-col rounded-t-[10px] h-[96%] mt-24 fixed bottom-0 left-0 right-0 z-50">
          <div className="p-4 rounded-t-[10px] flex-1 flex flex-col">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-white/20 mb-4" />

            {/* Assistant Header */}
            <div className="flex items-center justify-between px-2 pb-4">
              <motion.div initial={{ x: 0 }} animate={{ x: 0 }} className="flex items-center">
                <LogoButton size={40} onClick={onVoiceToggle} isActive={isRecording} />
                <div className="ml-3">
                  <h3 className="text-white font-medium">OpenAI Assistant</h3>
                  <p className="text-[#ffffffb2] text-sm">
                    {isConnecting
                      ? "Connecting..."
                      : isConnected
                      ? isRecording
                        ? "Listening..."
                        : "Tap to speak"
                      : "Initializing..."}
                  </p>
                </div>
              </motion.div>
              
              {/* Connection Status */}
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  isConnected ? "bg-green-500" : isConnecting ? "bg-yellow-500" : "bg-red-500"
                }`} />
                <span className="text-[#ffffffb2] text-sm">
                  {isConnected ? "Connected" : isConnecting ? "Connecting" : "Disconnected"}
                </span>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-500/20 border border-red-500/50 rounded-xl p-4 mb-4">
                <p className="text-white">{error}</p>
              </div>
            )}

            {/* Transcription Area */}
            <div className="flex-1 px-2 py-4 overflow-y-auto">
              <div className="bg-[#121624]/60 rounded-xl p-4 min-h-[100px] border border-[#ffffff]/10">
                <p className="text-white text-lg">
                  {transcription || (isConnected ? "Say something..." : "Waiting for connection...")}
                  {isRecording && <span className="inline-block w-2 h-5 bg-[#A4D321] ml-1 animate-pulse" />}
                </p>
              </div>
            </div>

            {/* Input Area at Bottom */}
            <div className="px-2 py-4 border-t border-[#ffffff]/10">
              <div className="flex items-center space-x-3">
                <motion.div initial={{ x: 0 }} animate={{ x: 0 }} className="flex-shrink-0">
                  <LogoButton size={40} onClick={onVoiceToggle} isActive={isRecording} />
                </motion.div>

                <div className="flex-1 bg-[#121624]/60 rounded-xl border border-[#ffffff]/10 px-4 py-2 min-h-[50px]">
                  <p className="text-white text-lg">
                    {isRecording && <span className="inline-block w-2 h-5 bg-[#A4D321] ml-1 animate-pulse" />}
                  </p>
                </div>
              </div>
            </div>

            {/* Call Information */}
            {callInfo && (
              <div className="px-2 py-4 border-t border-[#ffffff]/10">
                <h4 className="text-[#ffffffb2] mb-3">Call Information</h4>
                <div className="bg-[#121624]/60 rounded-xl p-4 border border-[#ffffff]/10">
                  <p className="text-white text-sm mb-1">Call ID: {String(callInfo.callId)}</p>
                  <p className="text-white text-sm mb-1">User ID: {String(callInfo.userId)}</p>
                  <p className="text-white text-sm">Call Type: {String(callInfo.callType)}</p>
                </div>
              </div>
            )}

            {/* Suggestions */}
            <div className="px-2 py-4">
              <h4 className="text-[#ffffffb2] mb-3">Suggestions</h4>
              <div className="flex flex-wrap gap-2">
                <motion.button
                  className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  disabled={!isConnected}
                >
                  What&apos;s the weather today?
                </motion.button>
                <motion.button
                  className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  disabled={!isConnected}
                >
                  Tell me about Stream Video
                </motion.button>
                <motion.button
                  className="px-4 py-2 rounded-full bg-[#121624]/60 border border-[#ffffff]/10 text-white text-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  disabled={!isConnected}
                >
                  How does OpenAI work?
                </motion.button>
              </div>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  )
}
