# ARA Property Services Application

The ARA Property Services App is a comprehensive property management solution designed to streamline inspections, maintenance tasks, and team coordination for property service providers.

## Features

- 🏢 **Multi-Tenant Architecture**: Support for multiple organizations with isolated data
- 🔐 **Secure Authentication**: Powered by Clerk for reliable, secure user management
- 🗣️ **Voice Assistant**: AI-powered voice interface for hands-free operation
- 💬 **Chat Interface**: Text-based AI assistant for property service inquiries
- 📱 **Mobile-First Design**: Optimized for field use on mobile devices
- 📋 **Inspection Management**: Create, conduct, and review property inspections
- 📅 **Task Scheduling**: Manage and assign cleaning and maintenance tasks
- 📊 **Reporting**: Generate and analyze property inspection reports
- 👥 **Team Coordination**: Assign tasks and monitor team performance

## Tech Stack

- **Framework**: Next.js 15 with React 19
- **Styling**: Tailwind CSS with custom theme
- **Authentication**: Clerk with organization support
- **Database**: Neon PostgreSQL with Drizzle ORM
- **AI Assistants**: Voice (ElevenLabs) and Chat (OpenAI)
- **State Management**: React Context API
- **Animation**: Framer Motion
- **Deployment**: Vercel

## Getting Started

### Prerequisites

- Node.js 18.x or later
- Bun 1.0 or later
- A Clerk account with organizations enabled
- A Convex account for backend services

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/danmarauda/askara-prod-final.git
   cd askara-prod-final
   ```

2. Install dependencies:
   ```bash
   bun install
   ```

3. Set up environment variables:
   Create a `.env.local` file with the following variables:
   ```
   # Clerk Authentication
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
   CLERK_SECRET_KEY=your_clerk_secret_key
   CLERK_WEBHOOK_SECRET=your_clerk_webhook_secret
   
   # Neon Database
   DATABASE_URL=your_neon_connection_string
   
   # ElevenLabs Voice Assistant
   AGENT_ID=your_elevenlabs_agent_id
   XI_API_KEY=your_elevenlabs_api_key
   
   # OpenAI (optional)
   OPENAI_API_KEY=your_openai_api_key
   ```

4. Push database schema:
   ```bash
   bun run db:push
   ```

5. Start the development server:
   ```bash
   bun dev
   ```

The application will be available at http://localhost:3000.

## Multi-Tenant Setup

The application supports multiple organizations with Clerk's organization features:

1. **Organization Creation**: Users can create new organizations through the Clerk interface
2. **Organization Switching**: Users can switch between organizations they belong to
3. **Data Isolation**: Each organization has its own properties, contracts, and other data
4. **Role-Based Access**: Different roles within organizations (admin, member, etc.)

The initial organization is set up as "ARA Property Services" during the database migration.

## Deployment

The application is configured for deployment on Vercel. See the [DEPLOYMENT-NOTES.md](./DEPLOYMENT-NOTES.md) file for detailed instructions on deployment.

## Documentation

Comprehensive documentation is available in the [docs/](./docs/) directory:

- **[Setup Guide](./docs/SETUP.md)** - Complete setup instructions for new developers
- **[Development Guide](./docs/DEVELOPMENT.md)** - Development workflow and best practices
- **[Deployment Guide](./docs/DEPLOYMENT.md)** - Deployment instructions and configuration
- **[Database Guide](./docs/DATABASE.md)** - Database setup and management
- **[Authentication Guide](./docs/AUTHENTICATION.md)** - Clerk authentication setup
- **[Troubleshooting Guide](./docs/TROUBLESHOOTING.md)** - Common issues and solutions
- **[Migration Status](./docs/MIGRATION_STATUS.md)** - Current Convex migration status

## License

This project is proprietary software owned by ARA Property Services.

## Contact

For inquiries or support, contact:
- <EMAIL>
