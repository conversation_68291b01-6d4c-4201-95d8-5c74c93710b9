"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Camera, CheckCircle, ChevronDown, ChevronUp, Clock, FileText, MapPin, Mic, X, Plus } from "lucide-react" // Removed unused Save
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"

interface InspectionFormScreenProps {
  onNavigate: (screen: string) => void
  userData: {
    name: string
    role: string
    department: string
  }
  inspectionId?: string
}

export function InspectionFormScreen({ onNavigate, userData, inspectionId }: InspectionFormScreenProps) {
  const [progress, setProgress] = useState(0)
  const [expandedSection, setExpandedSection] = useState<string | null>("general")
  // Removed unused 'selectedTab' state
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    general: {
      location: "",
      date: new Date().toISOString().split("T")[0],
      time: "",
      inspector: userData.name,
      type: "",
      notes: "",
    },
    safety: {
      emergencyExits: "",
      fireExtinguishers: "",
      firstAidKit: "",
      evacuationPlan: "",
      notes: "",
    },
    equipment: {
      hvacSystem: "",
      electricalPanels: "",
      plumbingFixtures: "",
      lightingSystem: "",
      notes: "",
    },
    compliance: {
      safetyRegulations: "",
      accessibilityStandards: "",
      environmentalCompliance: "",
      buildingCodes: "",
      notes: "",
    },
  })

  useEffect(() => {
    // If inspectionId is provided, fetch the inspection data
    if (inspectionId) {
      fetchInspectionData()
    }
  }, [inspectionId])

  const fetchInspectionData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // In a real app, this would be an API call
      // const response = await fetch(`/api/inspections/${inspectionId}`)
      // const data = await response.json()
      // setFormData(data.formData)
      // setProgress(data.progress || 0)
      // For now, just keep the empty form
    } catch (err: unknown) { // Type err as unknown
      console.error("Error fetching inspection data:", err)
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage || "Failed to load inspection data")
    } finally {
      setIsLoading(false)
    }
  }

  const toggleSection = (section: string) => {
    if (expandedSection === section) {
      setExpandedSection(null)
    } else {
      setExpandedSection(section)
    }
  }

  const handleStatusChange = (section: string, item: string, status: string) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [item]: status,
      },
    }))

    // Update progress based on form completion
    const totalItems =
      Object.keys(formData.safety).filter((k) => k !== "notes").length +
      Object.keys(formData.equipment).filter((k) => k !== "notes").length +
      Object.keys(formData.compliance).filter((k) => k !== "notes").length

    const completedItems =
      Object.entries(formData.safety).filter(([k, v]) => k !== "notes" && v !== "").length +
      Object.entries(formData.equipment).filter(([k, v]) => k !== "notes" && v !== "").length +
      Object.entries(formData.compliance).filter(([k, v]) => k !== "notes" && v !== "").length

    setProgress(Math.round((completedItems / totalItems) * 100))
  }

  const handleNotesChange = (section: string, notes: string) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        notes,
      },
    }))
  }

  // Removed unused function 'getStatusColor'
  // Removed unused function 'getStatusIcon'

  const handleFieldChange = (section: string, field: string, value: unknown) => { // Change value type to unknown
    try {
      // In a real app, this would be an API call to save the form data
      // const response = await fetch('/api/inspections', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(formData),
      // })

      // if (!response.ok) {
      //   throw new Error('Failed to submit inspection')
      // }

      // Navigate back to tasks screen
      onNavigate("tasks")
    } catch (err: unknown) { // Type err as unknown
      console.error("Error submitting inspection:", err)
      // Show error message to user
    }
  }

  if (isLoading) {
    return (
      <ScrollArea className="flex-1 w-full h-full">
        <div className="p-6">
          <div className="flex items-center justify-center p-12">
            <div className="w-8 h-8 border-2 border-[#A4D321] border-t-transparent rounded-full animate-spin" />
          </div>
        </div>
      </ScrollArea>
    )
  }

  if (error) {
    return (
      <ScrollArea className="flex-1 w-full h-full">
        <div className="p-6 text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <Button onClick={() => fetchInspectionData()}>Try Again</Button>
        </div>
      </ScrollArea>
    )
  }

  return (
    <ScrollArea className="flex-1 w-full h-full">
      <div className="p-6">
        {/* Progress Indicator */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <p className="text-white text-sm">Inspection Progress</p>
            <p className="text-[#A4D321] text-sm font-medium">{progress}%</p>
          </div>
          <Progress value={progress} className="h-2 bg-black/40" />
        </div>

        {/* Tabs */}
        <Tabs defaultValue="form" className="w-full mb-6">
          <TabsList className="w-full grid grid-cols-2 bg-black/40 backdrop-blur-sm mb-6">
            <TabsTrigger value="form" className="data-[state=active]:text-[#A4D321]">
              Form
            </TabsTrigger>
            <TabsTrigger value="photos" className="data-[state=active]:text-[#A4D321]">
              Photos & Media
            </TabsTrigger>
          </TabsList>

          <TabsContent value="form" className="space-y-4">
            {/* General Information */}
            <motion.div
              className="bg-black/30 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm"
              animate={{ height: expandedSection === "general" ? "auto" : "auto" }}
              transition={{ duration: 0.3 }}
            >
              <div
                className="p-4 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection("general")}
              >
                <h3 className="text-white text-base font-medium">General Information</h3>
                <motion.button>
                  {expandedSection === "general" ? (
                    <ChevronUp className="h-5 w-5 text-white" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-white" />
                  )}
                </motion.button>
              </div>

              {expandedSection === "general" && (
                <div className="px-4 pb-4 border-t border-white/10 pt-4">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <MapPin className="h-5 w-5 text-[#ffffffb2]" />
                      <div>
                        <p className="text-[#ffffffb2] text-xs">Location</p>
                        <input
                          type="text"
                          className="w-full mt-1 bg-black/40 border border-white/10 rounded-md p-2 text-white text-sm"
                          placeholder="Enter location"
                          value={formData.general.location}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              general: {
                                ...prev.general,
                                location: e.target.value,
                              },
                            }))
                          }
                        />
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Clock className="h-5 w-5 text-[#ffffffb2]" />
                      <div>
                        <p className="text-[#ffffffb2] text-xs">Date & Time</p>
                        <div className="flex gap-2 mt-1">
                          <input
                            type="date"
                            className="flex-1 bg-black/40 border border-white/10 rounded-md p-2 text-white text-sm"
                            value={formData.general.date}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                general: {
                                  ...prev.general,
                                  date: e.target.value,
                                },
                              }))
                            }
                          />
                          <input
                            type="time"
                            className="flex-1 bg-black/40 border border-white/10 rounded-md p-2 text-white text-sm"
                            value={formData.general.time}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                general: {
                                  ...prev.general,
                                  time: e.target.value,
                                },
                              }))
                            }
                          />
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-[#ffffffb2]" />
                      <div>
                        <p className="text-[#ffffffb2] text-xs">Notes</p>
                        <textarea
                          className="w-full mt-1 bg-black/40 border border-white/10 rounded-md p-2 text-white text-sm resize-none"
                          rows={3}
                          placeholder="Add general notes here..."
                          value={formData.general.notes}
                          onChange={(e) => handleNotesChange("general", e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>

            {/* Safety Checklist */}
            <motion.div
              className="bg-black/30 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm"
              animate={{ height: expandedSection === "safety" ? "auto" : "auto" }}
              transition={{ duration: 0.3 }}
            >
              <div
                className="p-4 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection("safety")}
              >
                <h3 className="text-white text-base font-medium">Safety Checklist</h3>
                <motion.button>
                  {expandedSection === "safety" ? (
                    <ChevronUp className="h-5 w-5 text-white" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-white" />
                  )}
                </motion.button>
              </div>

              {expandedSection === "safety" && (
                <div className="px-4 pb-4 border-t border-white/10 pt-4">
                  <div className="space-y-4">
                    {Object.entries(formData.safety)
                      .filter(([key]) => key !== "notes")
                      .map(([key, value]) => (
                        <div key={key} className="flex justify-between items-center">
                          <p className="text-white text-sm">
                            {key.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())}
                          </p>
                          <div className="flex gap-2">
                            <button
                              className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center",
                                value === "compliant"
                                  ? "bg-green-500/20 border-2 border-green-500"
                                  : "bg-black/40 border border-white/10",
                              )}
                              onClick={() => handleStatusChange("safety", key, "compliant")}
                            >
                              <CheckCircle
                                className={cn("h-4 w-4", value === "compliant" ? "text-green-500" : "text-white/50")}
                              />
                            </button>
                            <button
                              className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center",
                                value === "non-compliant"
                                  ? "bg-red-500/20 border-2 border-red-500"
                                  : "bg-black/40 border border-white/10",
                              )}
                              onClick={() => handleStatusChange("safety", key, "non-compliant")}
                            >
                              <X
                                className={cn("h-4 w-4", value === "non-compliant" ? "text-red-500" : "text-white/50")}
                              />
                            </button>
                            <button
                              className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center",
                                value === "attention"
                                  ? "bg-amber-500/20 border-2 border-amber-500"
                                  : "bg-black/40 border border-white/10",
                              )}
                              onClick={() => handleStatusChange("safety", key, "attention")}
                            >
                              <Clock
                                className={cn("h-4 w-4", value === "attention" ? "text-amber-500" : "text-white/50")}
                              />
                            </button>
                          </div>
                        </div>
                      ))}

                    <div className="pt-2">
                      <p className="text-white text-sm mb-2">Notes</p>
                      <textarea
                        className="w-full bg-black/40 border border-white/10 rounded-md p-2 text-white text-sm resize-none"
                        rows={3}
                        placeholder="Add safety notes here..."
                        value={formData.safety.notes}
                        onChange={(e) => handleNotesChange("safety", e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              )}
            </motion.div>

            {/* Equipment Checklist */}
            <motion.div
              className="bg-black/30 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm"
              animate={{ height: expandedSection === "equipment" ? "auto" : "auto" }}
              transition={{ duration: 0.3 }}
            >
              <div
                className="p-4 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection("equipment")}
              >
                <h3 className="text-white text-base font-medium">Equipment Checklist</h3>
                <motion.button>
                  {expandedSection === "equipment" ? (
                    <ChevronUp className="h-5 w-5 text-white" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-white" />
                  )}
                </motion.button>
              </div>

              {expandedSection === "equipment" && (
                <div className="px-4 pb-4 border-t border-white/10 pt-4">
                  <div className="space-y-4">
                    {Object.entries(formData.equipment)
                      .filter(([key]) => key !== "notes")
                      .map(([key, value]) => (
                        <div key={key} className="flex justify-between items-center">
                          <p className="text-white text-sm">
                            {key.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())}
                          </p>
                          <div className="flex gap-2">
                            <button
                              className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center",
                                value === "compliant"
                                  ? "bg-green-500/20 border-2 border-green-500"
                                  : "bg-black/40 border border-white/10",
                              )}
                              onClick={() => handleStatusChange("equipment", key, "compliant")}
                            >
                              <CheckCircle
                                className={cn("h-4 w-4", value === "compliant" ? "text-green-500" : "text-white/50")}
                              />
                            </button>
                            <button
                              className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center",
                                value === "non-compliant"
                                  ? "bg-red-500/20 border-2 border-red-500"
                                  : "bg-black/40 border border-white/10",
                              )}
                              onClick={() => handleStatusChange("equipment", key, "non-compliant")}
                            >
                              <X
                                className={cn("h-4 w-4", value === "non-compliant" ? "text-red-500" : "text-white/50")}
                              />
                            </button>
                            <button
                              className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center",
                                value === "attention"
                                  ? "bg-amber-500/20 border-2 border-amber-500"
                                  : "bg-black/40 border border-white/10",
                              )}
                              onClick={() => handleStatusChange("equipment", key, "attention")}
                            >
                              <Clock
                                className={cn("h-4 w-4", value === "attention" ? "text-amber-500" : "text-white/50")}
                              />
                            </button>
                          </div>
                        </div>
                      ))}

                    <div className="pt-2">
                      <p className="text-white text-sm mb-2">Notes</p>
                      <textarea
                        className="w-full bg-black/40 border border-white/10 rounded-md p-2 text-white text-sm resize-none"
                        rows={3}
                        placeholder="Add equipment notes here..."
                        value={formData.equipment.notes}
                        onChange={(e) => handleNotesChange("equipment", e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              )}
            </motion.div>

            {/* Compliance Checklist */}
            <motion.div
              className="bg-black/30 backdrop-blur-lg rounded-xl overflow-hidden border border-white/10 shadow-sm"
              animate={{ height: expandedSection === "compliance" ? "auto" : "auto" }}
              transition={{ duration: 0.3 }}
            >
              <div
                className="p-4 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection("compliance")}
              >
                <h3 className="text-white text-base font-medium">Compliance Checklist</h3>
                <motion.button>
                  {expandedSection === "compliance" ? (
                    <ChevronUp className="h-5 w-5 text-white" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-white" />
                  )}
                </motion.button>
              </div>

              {expandedSection === "compliance" && (
                <div className="px-4 pb-4 border-t border-white/10 pt-4">
                  <div className="space-y-4">
                    {Object.entries(formData.compliance)
                      .filter(([key]) => key !== "notes")
                      .map(([key, value]) => (
                        <div key={key} className="flex justify-between items-center">
                          <p className="text-white text-sm">
                            {key.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())}
                          </p>
                          <div className="flex gap-2">
                            <button
                              className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center",
                                value === "compliant"
                                  ? "bg-green-500/20 border-2 border-green-500"
                                  : "bg-black/40 border border-white/10",
                              )}
                              onClick={() => handleStatusChange("compliance", key, "compliant")}
                            >
                              <CheckCircle
                                className={cn("h-4 w-4", value === "compliant" ? "text-green-500" : "text-white/50")}
                              />
                            </button>
                            <button
                              className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center",
                                value === "non-compliant"
                                  ? "bg-red-500/20 border-2 border-red-500"
                                  : "bg-black/40 border border-white/10",
                              )}
                              onClick={() => handleStatusChange("compliance", key, "non-compliant")}
                            >
                              <X
                                className={cn("h-4 w-4", value === "non-compliant" ? "text-red-500" : "text-white/50")}
                              />
                            </button>
                            <button
                              className={cn(
                                "w-8 h-8 rounded-full flex items-center justify-center",
                                value === "attention"
                                  ? "bg-amber-500/20 border-2 border-amber-500"
                                  : "bg-black/40 border border-white/10",
                              )}
                              onClick={() => handleStatusChange("compliance", key, "attention")}
                            >
                              <Clock
                                className={cn("h-4 w-4", value === "attention" ? "text-amber-500" : "text-white/50")}
                              />
                            </button>
                          </div>
                        </div>
                      ))}

                    <div className="pt-2">
                      <p className="text-white text-sm mb-2">Notes</p>
                      <textarea
                        className="w-full bg-black/40 border border-white/10 rounded-md p-2 text-white text-sm resize-none"
                        rows={3}
                        placeholder="Add compliance notes here..."
                        value={formData.compliance.notes}
                        onChange={(e) => handleNotesChange("compliance", e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          </TabsContent>

          <TabsContent value="photos" className="space-y-4">
            {/* Photo Upload Section */}
            <div className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm">
              <h3 className="text-white text-base font-medium mb-4">Capture Media</h3>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <Button variant="outline" className="bg-black/40 border-white/10 text-white flex items-center gap-2">
                  <Camera className="h-4 w-4" /> Take Photo
                </Button>
                <Button variant="outline" className="bg-black/40 border-white/10 text-white flex items-center gap-2">
                  <Mic className="h-4 w-4" /> Record Audio
                </Button>
              </div>

              {/* Photo Gallery */}
              <div className="grid grid-cols-3 gap-2">
                <div className="aspect-square bg-black/40 rounded-md border border-dashed border-white/20 flex items-center justify-center">
                  <Plus className="h-6 w-6 text-white/50" />
                </div>
              </div>
            </div>

            {/* Voice Notes */}
            <div className="bg-black/30 backdrop-blur-lg rounded-xl p-4 border border-white/10 shadow-sm">
              <h3 className="text-white text-base font-medium mb-4">Voice Notes</h3>

              <div className="p-6 text-center">
                <p className="text-[#ffffffb2]">No voice notes recorded</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Submit Button */}
        <Button className="w-full bg-[#A4D321] text-black hover:bg-[#A4D321]/80 mb-4" onClick={() => console.log('Submit inspection')}>
          Submit Inspection
        </Button>
      </div>
    </ScrollArea>
  )
}
