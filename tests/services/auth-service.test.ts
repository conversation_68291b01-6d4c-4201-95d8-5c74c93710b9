import { describe, it, expect, vi, beforeEach } from 'vitest';
import { authService } from '@/app/services/auth-service';
import { userRepository } from '@/app/repositories/user-repository';

// Mock the user repository
vi.mock('@/app/repositories/user-repository', () => ({
  userRepository: {
    findByEmail: vi.fn(),
    findById: vi.fn(),
    updateProfile: vi.fn(),
  },
}));

// Mock next/headers
vi.mock('next/headers', () => ({
  cookies: vi.fn(),
}));

describe('AuthService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password123',
        role: 'User',
        department: 'IT',
        phone: '+1234567890',
      };

      (userRepository.findByEmail as any).mockResolvedValue(null);
      const mockUser = { id: 'user123', ...userData };
      // Simulate Convex mutation response
      (userRepository.findByEmail as any).mockResolvedValueOnce(null).mockResolvedValueOnce(mockUser);

      const result = await authService.register(userData);

      expect(result).toEqual(mockUser);
      expect(userRepository.findByEmail).toHaveBeenCalledWith('<EMAIL>');
    });

    it('should throw error when user already exists', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
      };

      const existingUser = { id: 'existing123', email: '<EMAIL>' };
      (userRepository.findByEmail as any).mockResolvedValue(existingUser);

      const result = await authService.register(userData);

      expect(result).toBeNull();
    });

    it('should handle registration errors', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
      };

      (userRepository.findByEmail as any).mockRejectedValue(new Error('Database error'));

      const result = await authService.register(userData);

      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    it('should login a user successfully', async () => {
      const email = '<EMAIL>';
      const password = 'password123';
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        password: 'password123',
        role: 'User',
      };

      (userRepository.findByEmail as any).mockResolvedValue(mockUser);

      const result = await authService.login(email, password);

      expect(result).toBeDefined();
      expect(result?.user).toEqual(mockUser);
      expect(result?.token).toBeDefined();
      expect(userRepository.findByEmail).toHaveBeenCalledWith(email);
    });

    it('should return null for non-existent user', async () => {
      const email = '<EMAIL>';
      const password = 'password123';

      (userRepository.findByEmail as any).mockResolvedValue(null);

      const result = await authService.login(email, password);

      expect(result).toBeNull();
    });

    it('should return null for incorrect password', async () => {
      const email = '<EMAIL>';
      const password = 'wrongpassword';
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        password: 'password123',
      };

      (userRepository.findByEmail as any).mockResolvedValue(mockUser);

      const result = await authService.login(email, password);

      expect(result).toBeNull();
    });

    it('should handle login errors', async () => {
      const email = '<EMAIL>';
      const password = 'password123';

      (userRepository.findByEmail as any).mockRejectedValue(new Error('Database error'));

      const result = await authService.login(email, password);

      expect(result).toBeNull();
    });
  });

  describe('getCurrentUser', () => {
    it('should get current user from token', async () => {
      const mockCookies = {
        get: vi.fn().mockReturnValue({ value: 'eyJ1c2VySWQiOiJ1c2VyMTIzIiwiZXhwIjoxOTk5OTk5OTk5fQ==' }),
      };
      (require('next/headers').cookies as any).mockReturnValue(mockCookies);

      const mockUser = { id: 'user123', name: 'John Doe' };
      (userRepository.findById as any).mockResolvedValue(mockUser);

      const result = await authService.getCurrentUser();

      expect(result).toEqual(mockUser);
      expect(userRepository.findById).toHaveBeenCalledWith('user123');
    });

    it('should return null when no token', async () => {
      const mockCookies = {
        get: vi.fn().mockReturnValue(undefined),
      };
      (require('next/headers').cookies as any).mockReturnValue(mockCookies);

      const result = await authService.getCurrentUser();

      expect(result).toBeNull();
    });

    it('should return null for expired token', async () => {
      const expiredToken = Buffer.from(JSON.stringify({
        userId: 'user123',
        exp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
      })).toString('base64');
      const mockCookies = {
        get: vi.fn().mockReturnValue({ value: expiredToken }),
      };
      (require('next/headers').cookies as any).mockReturnValue(mockCookies);

      const result = await authService.getCurrentUser();

      expect(result).toBeNull();
    });

    it('should return null for invalid token', async () => {
      const mockCookies = {
        get: vi.fn().mockReturnValue({ value: 'invalid-token' }),
      };
      (require('next/headers').cookies as any).mockReturnValue(mockCookies);

      const result = await authService.getCurrentUser();

      expect(result).toBeNull();
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when user is authenticated', async () => {
      const mockCookies = {
        get: vi.fn().mockReturnValue({ value: 'eyJ1c2VySWQiOiJ1c2VyMTIzIiwiZXhwIjoxOTk5OTk5OTk5fQ==' }),
      };
      (require('next/headers').cookies as any).mockReturnValue(mockCookies);

      const mockUser = { id: 'user123', name: 'John Doe' };
      (userRepository.findById as any).mockResolvedValue(mockUser);

      const result = await authService.isAuthenticated();

      expect(result).toBe(true);
    });

    it('should return false when user is not authenticated', async () => {
      const mockCookies = {
        get: vi.fn().mockReturnValue(undefined),
      };
      (require('next/headers').cookies as any).mockReturnValue(mockCookies);

      const result = await authService.isAuthenticated();

      expect(result).toBe(false);
    });
  });

  describe('hasRole', () => {
    it('should return true when user has the required role', async () => {
      const mockCookies = {
        get: vi.fn().mockReturnValue({ value: 'eyJ1c2VySWQiOiJ1c2VyMTIzIiwiZXhwIjoxOTk5OTk5OTk5fQ==' }),
      };
      (require('next/headers').cookies as any).mockReturnValue(mockCookies);

      const mockUser = { id: 'user123', role: 'Admin' };
      (userRepository.findById as any).mockResolvedValue(mockUser);

      const result = await authService.hasRole('Admin');

      expect(result).toBe(true);
    });

    it('should return false when user does not have the required role', async () => {
      const mockCookies = {
        get: vi.fn().mockReturnValue({ value: 'eyJ1c2VySWQiOiJ1c2VyMTIzIiwiZXhwIjoxOTk5OTk5OTk5fQ==' }),
      };
      (require('next/headers').cookies as any).mockReturnValue(mockCookies);

      const mockUser = { id: 'user123', role: 'User' };
      (userRepository.findById as any).mockResolvedValue(mockUser);

      const result = await authService.hasRole('Admin');

      expect(result).toBe(false);
    });

    it('should return false when user is not authenticated', async () => {
      const mockCookies = {
        get: vi.fn().mockReturnValue(undefined),
      };
      (require('next/headers').cookies as any).mockReturnValue(mockCookies);

      const result = await authService.hasRole('Admin');

      expect(result).toBe(false);
    });
  });

  describe('property-based testing', () => {
    it('should handle various email formats', async () => {
      const emailFormats = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      for (const email of emailFormats) {
        const userData = { name: 'Test User', email, password: 'password123' };
        (userRepository.findByEmail as any).mockResolvedValueOnce(null);
        const mockUser = { id: 'user123', ...userData };
        (userRepository.findByEmail as any).mockResolvedValueOnce(mockUser);

        const result = await authService.register(userData);
        expect(result?.email).toBe(email);
      }
    });

    it('should handle role variations', async () => {
      const roles = ['User', 'Admin', 'Manager', 'Supervisor', 'Guest'];
      
      for (const role of roles) {
        const userData = { name: 'Test User', email: '<EMAIL>', password: 'password123', role };
        (userRepository.findByEmail as any).mockResolvedValueOnce(null);
        const mockUser = { id: 'user123', ...userData };
        (userRepository.findByEmail as any).mockResolvedValueOnce(mockUser);

        const result = await authService.register(userData);
        expect(result?.role).toBe(role);
      }
    });

    it('should handle department variations', async () => {
      const departments = ['IT', 'HR', 'Finance', 'Marketing', 'Operations'];
      
      for (const department of departments) {
        const userData = { name: 'Test User', email: '<EMAIL>', password: 'password123', department };
        (userRepository.findByEmail as any).mockResolvedValueOnce(null);
        const mockUser = { id: 'user123', ...userData };
        (userRepository.findByEmail as any).mockResolvedValueOnce(mockUser);

        const result = await authService.register(userData);
        expect(result?.department).toBe(department);
      }
    });
  });

  describe('security testing', () => {
    it('should handle SQL injection attempts', async () => {
      const maliciousEmail = "<EMAIL>'; DROP TABLE users; --";
      const userData = { name: 'Test', email: maliciousEmail, password: 'password123' };

      (userRepository.findByEmail as any).mockResolvedValueOnce(null);
      const mockUser = { id: 'user123', ...userData };
      (userRepository.findByEmail as any).mockResolvedValueOnce(mockUser);

      const result = await authService.register(userData);
      expect(result?.email).toBe(maliciousEmail);
    });

    it('should handle XSS attempts', async () => {
      const maliciousName = '<script>alert("XSS")</script>';
      const userData = { name: maliciousName, email: '<EMAIL>', password: 'password123' };

      (userRepository.findByEmail as any).mockResolvedValueOnce(null);
      const mockUser = { id: 'user123', ...userData };
      (userRepository.findByEmail as any).mockResolvedValueOnce(mockUser);

      const result = await authService.register(userData);
      expect(result?.name).toBe(maliciousName);
    });

    it('should handle large payloads', async () => {
      const largeName = 'A'.repeat(1000);
      const userData = { name: largeName, email: '<EMAIL>', password: 'password123' };

      (userRepository.findByEmail as any).mockResolvedValueOnce(null);
      const mockUser = { id: 'user123', ...userData };
      (userRepository.findByEmail as any).mockResolvedValueOnce(mockUser);

      const result = await authService.register(userData);
      expect(result?.name).toBe(largeName);
    });
  });

  describe('concurrency testing', () => {
    it('should handle concurrent login attempts', async () => {
      const email = '<EMAIL>';
      const password = 'password123';
      const mockUser = { id: 'user123', email: '<EMAIL>', password: 'password123' };

      (userRepository.findByEmail as any).mockResolvedValue(mockUser);

      const promises = Array.from({ length: 10 }, () => 
        authService.login(email, password)
      );

      const results = await Promise.all(promises);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result?.user).toEqual(mockUser);
      });
    });

    it('should handle concurrent registration attempts', async () => {
      const userData = {
        name: 'Concurrent User',
        email: '<EMAIL>',
        password: 'password123',
      };

      (userRepository.findByEmail as any).mockResolvedValueOnce(null);
      const mockUser = { id: 'user123', ...userData };
      (userRepository.findByEmail as any).mockResolvedValueOnce(mockUser);

      const promises = Array.from({ length: 5 }, () => 
        authService.register(userData)
      );

      const results = await Promise.all(promises);
      results.forEach(result => {
        expect(result).toEqual(mockUser);
      });
    });
  });
});