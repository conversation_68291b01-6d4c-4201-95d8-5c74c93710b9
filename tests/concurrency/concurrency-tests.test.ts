import { describe, it, expect, vi } from 'vitest';
import { userRepository } from '@/app/repositories/user-repository';
import { propertyRepository } from '@/app/repositories/property-repository';
import { authService } from '@/app/services/auth-service';
import { api } from '@/convex/_generated/api';
import { createMockUser, createMockProperty, runConcurrent, createFaultyResponse, createSlowResponse, createFlakyResponse } from '../test-utils';

// Mock the Convex API
vi.mock('@/convex/_generated/api', () => ({
  api: {
    users: {
      findByEmail: vi.fn(),
      findById: vi.fn(),
      findByClerkId: vi.fn(),
      getCurrentUser: vi.fn(),
      listUsers: vi.fn(),
      searchUsers: vi.fn(),
      updateProfile: vi.fn(),
      findByRole: vi.fn(),
      findByDepartment: vi.fn(),
    },
    properties: {
      getProperty: vi.fn(),
      listProperties: vi.fn(),
      createProperty: vi.fn(),
      updateProperty: vi.fn(),
      deleteProperty: vi.fn(),
      searchProperties: vi.fn(),
      getPropertiesByManager: vi.fn(),
      getPropertiesByRegion: vi.fn(),
      getPropertiesByType: vi.fn(),
      getPropertiesByStatus: vi.fn(),
      createPropertyArea: vi.fn(),
      getPropertyAreas: vi.fn(),
      updatePropertyArea: vi.fn(),
      deletePropertyArea: vi.fn(),
    },
  },
}));

describe('Concurrency Tests', () => {
  describe('User Repository Concurrency', () => {
    it('should handle concurrent findByEmail operations', async () => {
      const mockUser = createMockUser({ email: '<EMAIL>' });
      (api.users.findByEmail as any).mockResolvedValue(mockUser);

      const promises = Array.from({ length: 10 }, () => 
        userRepository.findByEmail('<EMAIL>')
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result).toEqual(mockUser);
      });
      expect(api.users.findByEmail).toHaveBeenCalledTimes(10);
    });

    it('should handle concurrent search operations', async () => {
      const mockUsers = [createMockUser(), createMockUser()];
      (api.users.searchUsers as any).mockResolvedValue(mockUsers);

      const promises = Array.from({ length: 5 }, () => 
        userRepository.search('test')
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toEqual(mockUsers);
      });
    });

    it('should handle concurrent role-based queries', async () => {
      const mockUsers = [createMockUser({ role: 'Admin' })];
      (api.users.findByRole as any).mockResolvedValue(mockUsers);

      const promises = Array.from({ length: 8 }, () => 
        userRepository.findByRole('Admin')
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(8);
      results.forEach(result => {
        expect(result).toEqual(mockUsers);
      });
    });
  });

  describe('Property Repository Concurrency', () => {
    it('should handle concurrent findById operations', async () => {
      const mockProperty = createMockProperty({ _id: 'prop123' as any });
      (api.properties.getProperty as any).mockResolvedValue(mockProperty);

      const promises = Array.from({ length: 15 }, () => 
        propertyRepository.findById('prop123' as any)
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(15);
      results.forEach(result => {
        expect(result).toEqual(mockProperty);
      });
    });

    it('should handle concurrent property creation', async () => {
      const newProperty = {
        name: 'Test Property',
        address: '123 Test St',
        suburb: 'Testville',
        state: 'NSW',
        postcode: '2000',
        region: 'Sydney',
        type: 'Office',
        status: 'Active',
      };
      (api.properties.createProperty as any).mockResolvedValue('new-prop-id' as any);

      const promises = Array.from({ length: 5 }, () => 
        propertyRepository.create(newProperty)
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toBe('new-prop-id');
      });
    });

    it('should handle concurrent region queries', async () => {
      const mockProperties = [createMockProperty({ region: 'Sydney' })];
      (api.properties.getPropertiesByRegion as any).mockResolvedValue(mockProperties);

      const promises = Array.from({ length: 6 }, () => 
        propertyRepository.getByRegion('Sydney')
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(6);
      results.forEach(result => {
        expect(result).toEqual(mockProperties);
      });
    });
  });

  describe('Auth Service Concurrency', () => {
    it('should handle concurrent login attempts', async () => {
      const email = '<EMAIL>';
      const password = 'password123';
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        password: 'password123',
        role: 'User',
      };

      (userRepository.findByEmail as any).mockResolvedValue(mockUser);

      const promises = Array.from({ length: 10 }, () => 
        authService.login(email, password)
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result?.user).toEqual(mockUser);
      });
    });

    it('should handle concurrent registration attempts', async () => {
      const userData = {
        name: 'Concurrent User',
        email: '<EMAIL>',
        password: 'password123',
      };

      (userRepository.findByEmail as any).mockResolvedValueOnce(null);
      const mockUser = { id: 'user123', ...userData };
      (userRepository.findByEmail as any).mockResolvedValueOnce(mockUser);

      const promises = Array.from({ length: 5 }, () => 
        authService.register(userData)
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toEqual(mockUser);
      });
    });

    it('should handle concurrent authentication checks', async () => {
      const mockCookies = {
        get: vi.fn().mockReturnValue({ value: 'eyJ1c2VySWQiOiJ1c2VyMTIzIiwiZXhwIjoxOTk5OTk5OTk5fQ==' }),
      };
      (require('next/headers').cookies as any).mockReturnValue(mockCookies);

      const mockUser = { id: 'user123', role: 'User' };
      (userRepository.findById as any).mockResolvedValue(mockUser);

      const promises = Array.from({ length: 8 }, () => 
        authService.isAuthenticated()
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(8);
      results.forEach(result => {
        expect(result).toBe(true);
      });
    });
  });

  describe('Race Conditions', () => {
    it('should handle race conditions in user creation', async () => {
      const userData = {
        name: 'Race Condition User',
        email: '<EMAIL>',
        password: 'password123',
      };

      // Simulate race condition where multiple requests try to create same user
      (userRepository.findByEmail as any).mockResolvedValueOnce(null);
      const mockUser = { id: 'user123', ...userData };
      (userRepository.findByEmail as any).mockResolvedValueOnce(mockUser);

      const promises = Array.from({ length: 5 }, (_, index) => 
        new Promise(resolve => setTimeout(() => resolve(authService.register(userData)), Math.random() * 100))
      );

      const results = await Promise.all(promises);
      
      // All should succeed due to mock setup
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toEqual(mockUser);
      });
    });

    it('should handle race conditions in property updates', async () => {
      const propertyId = 'prop123' as any;
      const updateData = { name: 'Updated Name' };
      (api.properties.updateProperty as any).mockResolvedValue(propertyId);

      const promises = Array.from({ length: 5 }, (_, index) => 
        new Promise(resolve => setTimeout(() => resolve(propertyRepository.update(propertyId, updateData)), Math.random() * 50))
      );

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toBe(propertyId);
      });
    });
  });

  describe('Fault Injection', () => {
    it('should handle network failures gracefully', async () => {
      (api.users.findByEmail as any).mockImplementation(() => 
        createFaultyResponse(0.3)
      );

      const promises = Array.from({ length: 10 }, () => 
        userRepository.findByEmail('<EMAIL>').catch(() => null)
      );

      const results = await Promise.all(promises);

      // Some requests should fail, some should succeed
      const successfulRequests = results.filter(result => result !== null);
      expect(successfulRequests.length).toBeLessThanOrEqual(10);
    });

    it('should handle slow responses', async () => {
      (api.properties.listProperties as any).mockImplementation(() => 
        createSlowResponse(100)
      );

      const start = Date.now();
      const promises = Array.from({ length: 5 }, () => 
        propertyRepository.getAll()
      );

      const results = await Promise.all(promises);
      const end = Date.now();

      expect(results).toHaveLength(5);
      expect(end - start).toBeGreaterThanOrEqual(100);
    });

    it('should handle flaky services', async () => {
      (api.users.searchUsers as any).mockImplementation(() => 
        createFlakyResponse(0.5)
      );

      const promises = Array.from({ length: 8 }, () => 
        userRepository.search('test').catch(() => [])
      );

      const results = await Promise.all(promises);

      // Some requests should succeed despite flakiness
      const successfulRequests = results.filter(result => Array.isArray(result));
      expect(successfulRequests.length).toBeGreaterThan(0);
    });
  });

  describe('Load Testing', () => {
    it('should handle high load on user queries', async () => {
      const mockUser = createMockUser({ email: '<EMAIL>' });
      (api.users.findByEmail as any).mockResolvedValue(mockUser);

      const promises = Array.from({ length: 50 }, () => 
        userRepository.findByEmail('<EMAIL>')
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(50);
      results.forEach(result => {
        expect(result).toEqual(mockUser);
      });
    });

    it('should handle high load on property queries', async () => {
      const mockProperties = Array.from({ length: 100 }, (_, i) => 
        createMockProperty({ _id: `prop${i}` as any })
      );
      
      (api.properties.listProperties as any).mockResolvedValue(mockProperties);

      const promises = Array.from({ length: 30 }, () => 
        propertyRepository.getAll()
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(30);
      results.forEach(result => {
        expect(result).toEqual(mockProperties);
      });
    });

    it('should handle mixed operations under load', async () => {
      const mockUser = createMockUser();
      const mockProperty = createMockProperty();
      
      (api.users.findByEmail as any).mockResolvedValue(mockUser);
      (api.properties.getProperty as any).mockResolvedValue(mockProperty);

      const mixedOperations = [
        () => userRepository.findByEmail('<EMAIL>'),
        () => propertyRepository.findById('prop123' as any),
        () => userRepository.search('test'),
        () => propertyRepository.getAll(),
      ];

      const promises = Array.from({ length: 20 }, () => 
        mixedOperations[Math.floor(Math.random() * mixedOperations.length)]()
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(20);
      expect(results.filter(r => r && typeof r === 'object').length).toBeGreaterThan(0);
    });
  });

  describe('Resource Limits', () => {
    it('should handle memory pressure scenarios', async () => {
      const largeUserData = createMockUser({
        preferences: { theme: 'dark', notifications: true, data: new Array(1000).fill('test') }
      });
      
      (api.users.findByEmail as any).mockResolvedValue(largeUserData);

      const promises = Array.from({ length: 20 }, () => 
        userRepository.findByEmail('<EMAIL>')
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(20);
      results.forEach(result => {
        expect(result?.preferences?.data).toHaveLength(1000);
      });
    });

    it('should handle connection pool exhaustion', async () => {
      const mockProperties = Array.from({ length: 1000 }, (_, i) => 
        createMockProperty({ _id: `prop${i}` as any })
      );
      
      (api.properties.listProperties as any).mockResolvedValue(mockProperties);

      const promises = Array.from({ length: 100 }, () => 
        propertyRepository.getAll()
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(100);
      results.forEach(result => {
        expect(result).toHaveLength(1000);
      });
    });
  });
});