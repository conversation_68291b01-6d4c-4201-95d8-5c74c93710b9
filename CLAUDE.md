# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the **ARA Property Services Application** - a comprehensive property management solution for streamlining inspections, maintenance tasks, and team coordination. It features a modern mobile-first PWA design with AI-powered voice and chat assistants.

## Essential Development Commands

### Package Management
```bash
# Primary package manager is Bun
bun install                    # Install dependencies
bun dev                        # Start development server
bun run build                  # Build for production
bun start                      # Start production server
bun run lint                   # Run ESLint
bun test                       # Run Vitest test suite
bun run ci                     # Run CI checks (lint)
```

### Database Operations (Drizzle + Neon PostgreSQL)
```bash
bun run db:push                # Push schema changes to database
bun run db:studio              # Open Drizzle Studio (database GUI)
bun run db:generate            # Generate migrations
bun run db:migrate             # Run migrations
bun run db:check               # Verify database connection
bun run db:verify              # Verify schema integrity
bun run db:seed                # Seed database with test data
```

### Testing Commands
```bash
bun test                       # Run all tests with Vitest
bun run test:clerk             # Test Clerk integration
bun run test:neon              # Test Neon database connection
```

### Validation & Environment
```bash
bun run validate:env           # Validate environment variables
bun run validate:deployment    # Validate deployment readiness
bun run validate:all           # Run all validation checks
```

### Utility Commands
```bash
bun run clean                  # Clean build artifacts and node_modules
bun run reset                  # Full reset using ./scripts/reset-bun.sh
```

## Architecture Overview

### Core Technology Stack
- **Framework**: Next.js 15 with App Router, React 19
- **Database**: Dual architecture with Neon PostgreSQL (Drizzle ORM) and Convex (real-time)
- **Authentication**: Clerk with multi-organization support
- **Styling**: Tailwind CSS with Radix UI components
- **AI Features**: OpenAI integration, ElevenLabs voice assistant
- **Testing**: Vitest with 100% coverage thresholds
- **Type Safety**: TypeScript with strict configuration

### Dual Database Architecture

This project uses a **dual database approach**:

1. **Neon PostgreSQL** (`app/db/schema.ts`) - Primary relational database
   - Complex relational data (properties, inspections, contracts)
   - Comprehensive schema with proper foreign keys and indexing
   - Drizzle ORM for type-safe queries

2. **Convex** (`convex/schema.ts`) - Real-time database  
   - Real-time features (chat, notifications, live updates)
   - Simplified schema optimized for real-time operations
   - Built-in authentication and real-time subscriptions

### Multi-Tenant Organization Structure

The application implements **multi-tenancy through Clerk organizations**:
- All data is scoped to `organizationId`
- Organization-aware middleware enforces access control
- Organization switching supported in UI
- Role-based permissions (admin, member roles)

### Key Application Domains

1. **Property Management** - Properties, areas, contracts, specifications
2. **Inspection System** - Templates, reports, actions, attachments  
3. **Task Management** - Cleaning tasks, scheduling, assignments
4. **Communication** - Chat sessions, messages, notifications
5. **Analytics** - Dashboard metrics, KPI tracking

## Project Structure

```
app/
├── api/                      # Next.js API routes
│   ├── auth/                # Authentication endpoints  
│   ├── organizations/       # Organization management
│   ├── user/                # User profile management
│   └── webhooks/            # Clerk webhooks
├── db/                      # Neon database configuration
│   ├── schema.ts            # Drizzle schema (primary DB)
│   └── organizations.ts     # Organization-specific tables
├── repositories/            # Data access layer
├── services/                # Business logic layer
└── [routes]/                # Next.js app router pages

convex/
├── schema.ts                # Convex schema (real-time DB)
├── auth.config.ts           # Convex auth configuration
└── [functions]/             # Convex server functions

components/
├── ui/                      # Radix UI components
├── screens/                 # Mobile-first screen components
└── assistant/               # AI assistant components
```

## Authentication & Authorization

- **Provider**: Clerk with organization support
- **Middleware**: `middleware.ts` handles route protection
- **Organization Scoping**: All data queries must include `organizationId`
- **Webhooks**: Clerk webhook at `/api/webhooks/clerk` syncs organization data

## Environment Variables

Required environment variables (see `.env.example`):
```bash
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
CLERK_WEBHOOK_SECRET=

# Neon Database  
DATABASE_URL=

# Convex (for real-time features)
CONVEX_DEPLOYMENT=
NEXT_PUBLIC_CONVEX_URL=

# AI Features
XI_API_KEY=                   # ElevenLabs
AGENT_ID=                     # ElevenLabs Agent
OPENAI_API_KEY=               # OpenAI (optional)
```

## Testing Strategy

- **Framework**: Vitest with jsdom environment
- **Coverage**: 100% thresholds enforced for branches, functions, lines, statements
- **Structure**: Tests in `tests/` directory organized by domain
- **Setup**: `vitest.setup.ts` configures testing environment
- **Commands**: Run `bun test` for full test suite

## Key Development Patterns

### Data Access Pattern
Always use repository pattern for database operations:
```typescript
// Use repositories from app/repositories/
import { propertyRepository } from '@/app/repositories';
const properties = await propertyRepository.getByOrganization(orgId);
```

### Organization Scoping
All database queries must be scoped to organization:
```typescript
// Neon queries - always include organizationId
const results = await db.select().from(properties)
  .where(eq(properties.organizationId, organizationId));

// Convex queries - always filter by organizationId  
const results = await ctx.db.query("properties")
  .filter(q => q.eq(q.field("organizationId"), organizationId));
```

### Real-time Features
Use Convex for real-time data:
```typescript
// Use Convex for chat, notifications, live updates
const messages = useQuery(api.chat.getMessages, { sessionId });
```

## Mobile-First Design

- All components in `components/screens/` are mobile-optimized
- PWA-ready with manifest and service worker
- Touch-friendly interfaces with proper gesture handling
- Responsive design using Tailwind CSS mobile-first approach

## AI Integration

- **Voice Assistant**: ElevenLabs integration in `components/assistant/`
- **Chat Assistant**: OpenAI integration for property service queries
- **Voice Interface**: Real-time voice interaction for hands-free operation

## Production Deployment

- **Platform**: Vercel (configured via `vercel.json`)
- **Database**: Neon PostgreSQL for production
- **Real-time**: Convex deployment for real-time features
- **Validation**: Run `bun run validate:all` before deployment
- **Post-install**: Database migrations run automatically via `postinstall` script