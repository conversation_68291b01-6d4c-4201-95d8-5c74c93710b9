import { vi } from 'vitest';
import { Id } from '@/convex/_generated/dataModel';

// Test data factories
export const createMockUser = (overrides = {}) => ({
  _id: 'mock-user-id' as Id<"users">,
  clerkUserId: 'mock-clerk-id',
  email: '<EMAIL>',
  name: 'Test User',
  imageUrl: 'https://example.com/avatar.jpg',
  role: 'User',
  department: 'General',
  phone: '+1234567890',
  organizationId: 'mock-org-id' as Id<"organizations">,
  preferences: {},
  _creationTime: Date.now(),
  ...overrides,
});

export const createMockProperty = (overrides = {}) => ({
  _id: 'mock-property-id' as Id<"properties">,
  name: 'Test Property',
  address: '123 Test Street',
  suburb: 'Testville',
  state: 'NSW',
  postcode: '2000',
  region: 'Sydney',
  type: 'Office',
  status: 'Active',
  managerId: 'mock-manager-id' as Id<"users">,
  contractValue: 100000,
  organizationId: 'mock-org-id' as Id<"organizations">,
  _creationTime: Date.now(),
  ...overrides,
});

export const createMockPropertyArea = (overrides = {}) => ({
  _id: 'mock-area-id' as Id<"propertyAreas">,
  name: 'Test Area',
  description: 'Test area description',
  areaType: 'Office',
  size: 100,
  floor: 'Level 1',
  specialRequirements: ['Air conditioning'],
  propertyId: 'mock-property-id' as Id<"properties">,
  organizationId: 'mock-org-id' as Id<"organizations">,
  _creationTime: Date.now(),
  ...overrides,
});

export const createMockContact = (overrides = {}) => ({
  id: 'mock-contact-id',
  name: 'Test Contact',
  role: 'Manager',
  company: 'Test Company',
  phone: '+1234567890',
  email: '<EMAIL>',
  location: 'Sydney, NSW',
  is_favorite: false,
  avatar: 'https://example.com/avatar.jpg',
  created_at: new Date(),
  updated_at: new Date(),
  ...overrides,
});

export const createMockMessage = (overrides = {}) => ({
  id: 'mock-message-id',
  session_id: 'mock-session-id',
  content_type: 'text',
  content_transcript: 'Test message',
  object: 'message',
  role: 'user',
  status: 'sent',
  type: 'text',
  sender_id: 'mock-user-id',
  created_at: new Date(),
  ...overrides,
});

// Property-based testing utilities
export const generateRandomString = (length = 10) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

export const generateRandomEmail = () => {
  return `${generateRandomString(8)}@example.com`;
};

export const generateRandomPhone = () => {
  return `+${Math.floor(Math.random() * 9000000000) + 1000000000}`;
};

export const generateRandomAddress = () => {
  const streets = ['Main St', 'Oak Ave', 'Pine Rd', 'Elm Blvd', 'First St'];
  const suburbs = ['Downtown', 'Uptown', 'Midtown', 'Westside', 'Eastside'];
  const states = ['NSW', 'VIC', 'QLD', 'WA', 'SA', 'TAS', 'ACT', 'NT'];
  
  return {
    address: `${Math.floor(Math.random() * 9999)} ${streets[Math.floor(Math.random() * streets.length)]}`,
    suburb: suburbs[Math.floor(Math.random() * suburbs.length)],
    state: states[Math.floor(Math.random() * states.length)],
    postcode: String(Math.floor(Math.random() * 9000) + 1000),
  };
};

// Async testing utilities
export const waitFor = async <T>(
  callback: () => T,
  timeout = 5000,
  interval = 50
): Promise<T> => {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    try {
      const result = callback();
      if (result !== undefined && result !== null) {
        return result;
      }
    } catch (error) {
      // Continue waiting
    }
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  throw new Error('Timeout waiting for condition');
};

export const retry = async <T>(
  fn: () => Promise<T>,
  retries = 3,
  delay = 100
): Promise<T> => {
  try {
    return await fn();
  } catch (error) {
    if (retries > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
      return retry(fn, retries - 1, delay * 2);
    }
    throw error;
  }
};

// Performance testing utilities
export const measurePerformance = async <T>(
  fn: () => Promise<T>,
  iterations = 100
): Promise<{
  average: number;
  min: number;
  max: number;
  total: number;
  results: T[];
}> => {
  const times: number[] = [];
  const results: T[] = [];
  
  for (let i = 0; i < iterations; i++) {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    
    times.push(end - start);
    results.push(result);
  }
  
  return {
    average: times.reduce((a, b) => a + b, 0) / times.length,
    min: Math.min(...times),
    max: Math.max(...times),
    total: times.reduce((a, b) => a + b, 0),
    results,
  };
};

// Security testing utilities
export const createMaliciousInput = () => ({
  xss: '<script>alert("XSS")</script>',
  sqlInjection: "'; DROP TABLE users; --",
  pathTraversal: '../../../etc/passwd',
  commandInjection: '; rm -rf /',
  largePayload: 'a'.repeat(1000000),
  nullBytes: 'test\x00',
  unicode: 'test\u0000\u0001\u0002',
  specialChars: '<>&"\'',
});

// Concurrency testing utilities
export const runConcurrent = async <T>(
  fn: () => Promise<T>,
  concurrency = 10
): Promise<T[]> => {
  const promises = Array.from({ length: concurrency }, () => fn());
  return Promise.all(promises);
};

export const runWithRaceCondition = async <T>(
  fn: () => Promise<T>,
  delay = 10
): Promise<T[]> => {
  const results: T[] = [];
  const promises = Array.from({ length: 5 }, async () => {
    await new Promise(resolve => setTimeout(resolve, Math.random() * delay));
    const result = await fn();
    results.push(result);
    return result;
  });
  
  await Promise.all(promises);
  return results;
};

// Fault injection utilities
export const createFaultyResponse = (errorRate = 0.1) => {
  return Math.random() < errorRate
    ? Promise.reject(new Error('Simulated network error'))
    : Promise.resolve({ success: true });
};

export const createSlowResponse = (delay = 1000) => {
  return new Promise(resolve => setTimeout(() => resolve({ success: true }), delay));
};

export const createFlakyResponse = (failureRate = 0.3) => {
  return Math.random() < failureRate
    ? Promise.reject(new Error('Flaky service error'))
    : Promise.resolve({ data: 'success' });
};

// Property-based testing generators
export const generateUserProperties = () => ({
  name: generateRandomString(5 + Math.floor(Math.random() * 20)),
  email: generateRandomEmail(),
  role: ['User', 'Admin', 'Manager', 'Supervisor'][Math.floor(Math.random() * 4)],
  department: ['IT', 'HR', 'Finance', 'Operations', 'Marketing'][Math.floor(Math.random() * 5)],
  phone: generateRandomPhone(),
});

export const generatePropertyProperties = () => {
  const address = generateRandomAddress();
  return {
    name: generateRandomString(5 + Math.floor(Math.random() * 15)),
    ...address,
    type: ['Office', 'Retail', 'Industrial', 'Residential'][Math.floor(Math.random() * 4)],
    status: ['Active', 'Inactive', 'Pending', 'Archived'][Math.floor(Math.random() * 4)],
  };
};

// Validation utilities
export const validateEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone: string) => {
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(phone);
};

export const validatePostcode = (postcode: string) => {
  const postcodeRegex = /^\d{4}$/;
  return postcodeRegex.test(postcode);
};

// Test data builders
export class TestDataBuilder {
  private user: any = {};
  private property: any = {};
  private areas: any[] = [];

  withUser(user: any) {
    this.user = { ...this.user, ...user };
    return this;
  }

  withProperty(property: any) {
    this.property = { ...this.property, ...property };
    return this;
  }

  withArea(area: any) {
    this.areas.push(area);
    return this;
  }

  build() {
    return {
      user: createMockUser(this.user),
      property: createMockProperty(this.property),
      areas: this.areas.map(area => createMockPropertyArea(area)),
    };
  }
}

// Snapshot testing utilities
export const createSnapshot = (data: any) => {
  return JSON.stringify(data, null, 2);
};