import { NextResponse } from "next/server"
import { openai } from "@ai-sdk/openai"
import { streamText, CoreMessage } from "ai" // Import CoreMessage
import type { StreamTextResult } from "ai" // Import StreamTextResult for type hint

export async function POST(request: Request) {
  try {
    const { message, apiKey, history = [] } = await request.json()

    if (!message) {
      return NextResponse.json({ error: "Message is required" }, { status: 400 })
    }

    if (!apiKey) {
      return NextResponse.json({ error: "API key is required" }, { status: 400 })
    }

    // Prepare the conversation history for the AI
    const formattedHistory: CoreMessage[] = history.map((msg: CoreMessage) => ({ // Type msg as CoreMessage
      role: msg.role,
      content: msg.content,
    }))

    // Add the new message to the history
    formattedHistory.push({
      role: "user",
      content: message,
    })

    // Create a stream transformer
    const stream = await streamText({
      model: openai("gpt-4o"),
      prompt: message,
      messages: formattedHistory,
    })

    return NextResponse.json({
      text: await stream.text, // Await the text property
      id: Date.now().toString(),
      role: "assistant",
    })
  } catch (error: unknown) { // Type error as unknown
    console.error("Error in chat API:", error)
    const errorMessage = error instanceof Error ? error.message : String(error);
    return NextResponse.json({ error: `Failed to generate response: ${errorMessage}` }, { status: 500 })
  }
}
