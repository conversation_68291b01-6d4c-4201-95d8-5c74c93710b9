/**
 * Multi-tenant testing script
 * Tests organization data isolation in Convex
 */
import { ConvexHttpClient } from "convex/browser";
import { api } from "./convex/_generated/api.js";
import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL);

async function runMultiTenantTests() {
  console.log("🧪 Starting Multi-Tenant Tests...\n");

  try {
    // Step 1: Clean up any existing test data
    console.log("1. Cleaning up existing test data...");
    const cleanup1 = await convex.mutation(api.test_multi_tenant.cleanupTestData, {});
    console.log(`   ✅ Cleaned up ${cleanup1.deletedCount} records\n`);

    // Step 2: Create test organizations
    console.log("2. Creating test organizations...");
    const orgs = await convex.mutation(api.test_multi_tenant.createTestOrganizations, {});
    console.log(`   ✅ Created organizations: ${orgs.org1Id}, ${orgs.org2Id}\n`);

    // Step 3: Create test users
    console.log("3. Creating test users...");
    const users = await convex.mutation(api.test_multi_tenant.createTestUsers, {
      org1Id: orgs.org1Id,
      org2Id: orgs.org2Id
    });
    console.log(`   ✅ Created users in both organizations\n`);

    // Step 4: Create test properties
    console.log("4. Creating test properties...");
    const properties = await convex.mutation(api.test_multi_tenant.createTestProperties, {
      org1Id: orgs.org1Id,
      org2Id: orgs.org2Id,
      user1Id: users.user1Id,
      user3Id: users.user3Id
    });
    console.log(`   ✅ Created properties in both organizations\n`);

    // Step 5: Test Org1 data isolation
    console.log("5. Testing Organization 1 data isolation...");
    const org1Data = await convex.query(api.test_multi_tenant.testOrg1DataIsolation, {
      org1Id: orgs.org1Id,
      user1Id: users.user1Id
    });
    console.log(`   📊 Org 1 Users: ${org1Data.org1UsersCount} (Expected: 2)`);
    console.log(`   📊 Org 1 Properties: ${org1Data.org1PropertiesCount} (Expected: 2)`);
    console.log(`   👥 Users: ${org1Data.org1Users.map(u => u.name).join(", ")}`);
    console.log(`   🏢 Properties: ${org1Data.org1Properties.map(p => p.name).join(", ")}\n`);

    // Step 6: Test Org2 data isolation
    console.log("6. Testing Organization 2 data isolation...");
    const org2Data = await convex.query(api.test_multi_tenant.testOrg2DataIsolation, {
      org2Id: orgs.org2Id,
      user3Id: users.user3Id
    });
    console.log(`   📊 Org 2 Users: ${org2Data.org2UsersCount} (Expected: 2)`);
    console.log(`   📊 Org 2 Properties: ${org2Data.org2PropertiesCount} (Expected: 2)`);
    console.log(`   👥 Users: ${org2Data.org2Users.map(u => u.name).join(", ")}`);
    console.log(`   🏢 Properties: ${org2Data.org2Properties.map(p => p.name).join(", ")}\n`);

    // Step 7: Test cross-organization access prevention
    console.log("7. Testing cross-organization access prevention...");
    const crossOrgTest = await convex.query(api.test_multi_tenant.testCrossOrgAccessPrevention, {
      org1Id: orgs.org1Id,
      org2Id: orgs.org2Id
    });
    console.log(`   🛡️  Cross-org users found: ${crossOrgTest.crossOrgUsersFound} (Should be: 0)`);
    console.log(`   🛡️  Cross-org properties found: ${crossOrgTest.crossOrgPropertiesFound} (Should be: 0)`);
    console.log(`   🛡️  Data isolation working: ${crossOrgTest.shouldBeZero ? "✅ YES" : "❌ NO"}\n`);

    // Step 8: Final validation
    console.log("8. Final validation...");
    const allChecks = [
      org1Data.org1UsersCount === 2,
      org1Data.org1PropertiesCount === 2,
      org2Data.org2UsersCount === 2,
      org2Data.org2PropertiesCount === 2,
      crossOrgTest.shouldBeZero
    ];

    const passed = allChecks.filter(Boolean).length;
    const total = allChecks.length;

    console.log(`   📈 Tests passed: ${passed}/${total}`);
    
    if (passed === total) {
      console.log("\n🎉 Multi-Tenant Tests PASSED! ✅");
      console.log("   ✅ Organizations are properly isolated");
      console.log("   ✅ Users can only access their organization's data");
      console.log("   ✅ Properties are scoped to organizations");
      console.log("   ✅ Cross-organization access is prevented");
    } else {
      console.log("\n❌ Multi-Tenant Tests FAILED!");
      console.log("   Some data isolation issues were detected");
    }

    // Step 9: Clean up test data
    console.log("\n9. Cleaning up test data...");
    const cleanup2 = await convex.mutation(api.test_multi_tenant.cleanupTestData, {});
    console.log(`   ✅ Cleaned up ${cleanup2.deletedCount} records`);

    console.log("\n🏁 Multi-Tenant Testing Complete!");

  } catch (error) {
    console.error("\n❌ Error during multi-tenant testing:", error);
    
    // Try to clean up on error
    try {
      console.log("\n🧹 Attempting cleanup after error...");
      const cleanup = await convex.mutation(api.test_multi_tenant.cleanupTestData, {});
      console.log(`   ✅ Cleaned up ${cleanup.deletedCount} records`);
    } catch (cleanupError) {
      console.error("   ❌ Cleanup failed:", cleanupError);
    }
  }
}

// Run the tests
runMultiTenantTests();